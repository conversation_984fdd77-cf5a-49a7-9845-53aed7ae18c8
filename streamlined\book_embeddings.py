"""
Streamlined A.T.L.A.S Trading System - Trading Book Content
Curated content from top trading books for RAG education system
"""

from typing import Dict, List
from models import BookContent

# Trading in the Zone - Mark Douglas (Enhanced Content)
TRADING_IN_THE_ZONE_CONTENT = {
    "psychology": [
        BookContent(
            book_title="Trading in the Zone",
            chapter="Chapter 1",
            section="The Psychology of Trading",
            content="""The market is a reflection of mass psychology. Every price movement represents the collective beliefs, expectations, and emotions of all market participants at that moment. Understanding this psychological foundation is crucial for successful trading.

The biggest challenge traders face is not learning to analyze markets, but learning to think in probabilities and manage their emotions. Most traders fail because they approach the market with the wrong mindset - they want certainty in an uncertain environment.

To be consistently successful, you must learn to think like a casino. Casinos know that each individual bet is random, but over a series of bets, the odds are in their favor. Similarly, each trade is uncertain, but with proper risk management and a statistical edge, you can be profitable over time.

PRACTICAL APPLICATION: Before each trade, ask yourself: 'What is my edge?' and 'Am I thinking in probabilities or trying to predict this single outcome?' This mental check prevents emotional decision-making and keeps you focused on your systematic approach.""",
            concepts=["psychology", "mass_psychology", "emotions", "probability_thinking", "uncertainty", "edge", "systematic_approach"]
        ),
        BookContent(
            book_title="Trading in the Zone",
            chapter="Chapter 1",
            section="The Fundamental vs Technical Mindset",
            content="""Most traders get trapped in analysis paralysis because they believe more information equals better trades. This is fundamentally wrong. The market discounts all available information instantly. What matters is not what you know, but how you think about what you know.

Technical analysis works not because patterns predict the future, but because they reveal the collective psychology of market participants. When you see a head and shoulders pattern, you're seeing the emotional journey of traders from optimism to fear to capitulation.

The key insight: Markets are not logical, they are psychological. Price movements reflect human emotions more than fundamental values. This is why a stock can drop 20% on 'good' earnings - the market had already priced in even better expectations.""",
            concepts=["technical_analysis", "market_psychology", "information_processing", "patterns", "expectations", "price_action"]
        ),
        BookContent(
            book_title="Trading in the Zone",
            chapter="Chapter 2", 
            section="Fundamental vs Technical Analysis",
            content="""The market discounts everything. All fundamental information - earnings, economic data, news events - is already reflected in price. What moves markets is not the news itself, but how traders interpret and react to that news.

Technical analysis works because it reveals the collective psychology of market participants. Chart patterns and indicators show us how traders are thinking and feeling about a stock or market. When you understand this, you can anticipate how they might react to future price movements.

The key insight is that markets are driven by human emotions - fear and greed. These emotions create predictable patterns that repeat over time. Technical analysis helps us identify these patterns and profit from them.""",
            concepts=["technical_analysis", "market_psychology", "price_action", "patterns", "emotions"]
        )
    ],
    "discipline": [
        BookContent(
            book_title="Trading in the Zone",
            chapter="Chapter 3",
            section="Developing Discipline",
            content="""Consistency comes from developing a trading mindset that accepts uncertainty and focuses on process over outcomes. Most traders sabotage themselves by focusing on individual trades rather than their long-term edge.

Discipline means following your trading plan regardless of how you feel about a particular trade. It means taking every setup that meets your criteria, cutting losses quickly, and letting winners run. This requires emotional detachment from individual outcomes.

The disciplined trader understands that losses are simply the cost of doing business. They don't take losses personally or let them affect future decisions. Instead, they view each trade as one in a series of trades where their edge will play out over time.

PRACTICAL DISCIPLINE CHECKLIST:
1. Pre-market: Review your trading plan and rules
2. During market: Follow rules mechanically, no exceptions
3. Post-market: Journal what you did right and wrong
4. Weekly: Review performance and adjust rules if needed
5. Monthly: Analyze emotional patterns and triggers

DISCIPLINE MANTRAS:
- 'I trade my plan, not my emotions'
- 'This trade doesn't define me'
- 'Losses are tuition for market education'
- 'My edge plays out over many trades, not one'""",
            concepts=["discipline", "consistency", "trading_plan", "emotional_detachment", "process_focus", "journaling", "mantras", "rules"]
        ),
        BookContent(
            book_title="Trading in the Zone",
            chapter="Chapter 4",
            section="The Mechanics of Discipline",
            content="""True discipline in trading comes from understanding that you have complete control over your actions, but no control over market outcomes. This paradox is what separates successful traders from the rest.

The five fundamental truths of trading:
1. Anything can happen in the market
2. You don't need to know what will happen to make money
3. There is a random distribution between wins and losses
4. An edge is nothing more than a higher probability of one outcome over another
5. Every moment in the market is unique

When you truly accept these truths, you stop trying to predict and start focusing on executing your edge consistently. This is when trading becomes mechanical and emotions become irrelevant.""",
            concepts=["discipline_mechanics", "fundamental_truths", "edge_execution", "randomness", "probability", "mechanical_trading"]
        )
    ],
    "risk_management": [
        BookContent(
            book_title="Trading in the Zone",
            chapter="Chapter 4",
            section="Risk and Money Management",
            content="""Every trade has an uncertain outcome. Before entering any position, you must define your risk and accept that you could lose that amount. This pre-acceptance of risk eliminates the emotional stress that causes poor decision-making.

Position sizing is more important than entry technique. Never risk more than you can afford to lose on any single trade. Most successful traders risk no more than 1-2% of their account on any individual position.

The goal is not to be right on every trade, but to make money over a series of trades. This requires cutting losses short and letting profits run. Your winners must be bigger than your losers for you to be profitable long-term.""",
            concepts=["risk_management", "position_sizing", "loss_acceptance", "money_management", "risk_reward"]
        )
    ]
}

# Market Wizards - Jack Schwager (Enhanced Content)
MARKET_WIZARDS_CONTENT = {
    "interviews": [
        BookContent(
            book_title="Market Wizards",
            chapter="Chapter 1",
            section="Ed Seykota Interview - Risk Management Master",
            content="""Ed Seykota emphasizes that successful trading is about managing risk, not predicting markets. His key insights include: 'The elements of good trading are cutting losses, cutting losses, and cutting losses. If you can follow these three rules, you may have a chance.'

Seykota believes that everyone gets what they want from the market. If you're losing money, examine what you might unconsciously want from trading. Many people use trading to punish themselves or to experience excitement rather than to make money.

The trend is your friend until it bends. Seykota's approach focuses on riding long-term trends and cutting losses quickly when trends change. He emphasizes that the market will teach you everything you need to know if you listen to it.

SEYKOTA'S PRACTICAL RULES:
1. Cut losses immediately when trend changes
2. Position size based on volatility (risk parity)
3. Never add to losing positions
4. Let profits run until trend reversal
5. Trade with the trend, never against it
6. Use stops religiously - no exceptions
7. Focus on process, not profits

SEYKOTA'S TREND FOLLOWING SYSTEM:
- Entry: 20-day breakout above previous high
- Exit: 10-day breakout below previous low
- Position size: 2% risk per trade based on ATR
- No fundamental analysis - pure price action""",
            concepts=["risk_management", "trend_following", "psychology", "loss_cutting", "market_listening", "breakout_system", "position_sizing", "atr"]
        ),
        BookContent(
            book_title="Market Wizards", 
            chapter="Chapter 2",
            section="Richard Dennis Interview",
            content="""Richard Dennis proved that trading can be taught through his famous Turtle experiment. He took novice traders and taught them a simple trend-following system, demonstrating that success comes from following rules, not from innate talent.

The Turtle system was based on breakouts - buying when prices broke above recent highs and selling when they broke below recent lows. The key was position sizing based on volatility and strict adherence to the rules.

Dennis emphasized that most people lose money trading because they can't follow rules consistently. They let emotions override their system. The successful Turtles were those who could mechanically follow the system without second-guessing it.""",
            concepts=["trend_following", "breakouts", "position_sizing", "rule_following", "systematic_trading"]
        )
    ],
    "strategies": [
        BookContent(
            book_title="Market Wizards",
            chapter="Chapter 3", 
            section="Common Trading Strategies",
            content="""The most successful traders in Market Wizards used different approaches, but they shared common principles: they all had a systematic approach, they managed risk carefully, and they were disciplined in following their methods.

Trend followers like Seykota and Dennis focused on catching major moves and riding them for months or years. They accepted many small losses in exchange for occasional large profits. Their systems were designed to capture the few big moves that generated most of their profits.

Contrarian traders looked for extreme sentiment readings and bet against the crowd. They understood that markets often overreact to news, creating opportunities for those willing to take the other side of emotional trades.""",
            concepts=["systematic_trading", "trend_following", "contrarian_trading", "risk_management", "discipline"]
        )
    ]
}

# Technical Analysis Explained - Martin Pring
TECHNICAL_ANALYSIS_EXPLAINED_CONTENT = {
    "charts": [
        BookContent(
            book_title="Technical Analysis Explained",
            chapter="Chapter 1",
            section="Chart Basics",
            content="""Charts are visual representations of the battle between buyers and sellers. Every price movement reflects the changing balance of supply and demand. Understanding this basic concept is fundamental to reading charts effectively.

Support and resistance levels represent areas where the balance between buyers and sellers has shifted in the past. Support is a price level where buying interest has previously emerged, while resistance is where selling pressure has appeared.

Volume confirms price movements. When prices move on high volume, it suggests strong conviction behind the move. When prices move on low volume, the move may lack staying power and could easily reverse.""",
            concepts=["charts", "support_resistance", "supply_demand", "volume", "price_action"]
        )
    ],
    "patterns": [
        BookContent(
            book_title="Technical Analysis Explained",
            chapter="Chapter 2",
            section="Chart Patterns",
            content="""Chart patterns are formations that tend to repeat because they reflect consistent human behavior. Head and shoulders patterns, triangles, and double tops/bottoms are examples of how traders' emotions create recognizable formations.

Continuation patterns like flags and pennants suggest that the current trend will resume after a brief pause. Reversal patterns like head and shoulders suggest that the trend is changing direction.

The key to using patterns successfully is to wait for confirmation. A pattern is not complete until price breaks out of the formation with volume. Many apparent patterns fail, so patience and confirmation are essential.""",
            concepts=["chart_patterns", "continuation_patterns", "reversal_patterns", "confirmation", "breakouts"]
        )
    ],
    "indicators": [
        BookContent(
            book_title="Technical Analysis Explained",
            chapter="Chapter 3",
            section="Technical Indicators",
            content="""Technical indicators are mathematical calculations based on price and volume data. They help identify trends, momentum, and potential reversal points. However, indicators should supplement, not replace, basic chart analysis.

Momentum indicators like RSI and MACD help identify when a trend is losing steam. When price makes new highs but momentum indicators don't, it suggests the trend may be weakening (divergence).

Moving averages smooth out price action and help identify the underlying trend. When price is above the moving average, the trend is up. When below, the trend is down. Crossovers of different moving averages can signal trend changes.""",
            concepts=["technical_indicators", "momentum", "divergence", "moving_averages", "trend_identification"]
        )
    ]
}

# How to Make Money in Stocks - William O'Neil
HOW_TO_MAKE_MONEY_CONTENT = {
    "can_slim": [
        BookContent(
            book_title="How to Make Money in Stocks",
            chapter="Chapter 1",
            section="CAN SLIM System",
            content="""The CAN SLIM system identifies growth stocks with the highest probability of significant price appreciation. Each letter represents a key characteristic to look for:

C - Current quarterly earnings should be up 25% or more
A - Annual earnings growth should be 25% or more for the last 3 years  
N - New products, services, management, or price highs
S - Supply and demand - look for stocks with small float and institutional buying
L - Leader or laggard - buy the leaders in strong industry groups
I - Institutional sponsorship - funds should be buying the stock
M - Market direction - only buy when the general market is in an uptrend

This system combines fundamental analysis (earnings growth) with technical analysis (price action and volume) to identify the best growth stocks.""",
            concepts=["can_slim", "growth_stocks", "earnings_growth", "institutional_buying", "market_direction"]
        )
    ],
    "breakouts": [
        BookContent(
            book_title="How to Make Money in Stocks",
            chapter="Chapter 2",
            section="Breakout Patterns",
            content="""The best time to buy a growth stock is when it breaks out of a proper base pattern on heavy volume. A base is a sideways price consolidation that lasts at least 7-8 weeks and corrects 20-50% from the prior high.

Cup-with-handle patterns are among the most reliable. The cup forms as the stock declines and then recovers to near the old high. The handle is a smaller decline that shakes out weak holders before the breakout.

Volume should increase significantly on the breakout day - at least 50% above average. This confirms that institutions are accumulating the stock. Without volume confirmation, many breakouts fail.""",
            concepts=["breakouts", "base_patterns", "cup_with_handle", "volume_confirmation", "institutional_accumulation"]
        )
    ]
}

# The New Trading for a Living - Alexander Elder
NEW_TRADING_FOR_LIVING_CONTENT = {
    "triple_screen": [
        BookContent(
            book_title="The New Trading for a Living",
            chapter="Chapter 1",
            section="Triple Screen Trading System",
            content="""The Triple Screen system uses three different timeframes to analyze trades. This multi-timeframe approach helps identify high-probability setups by aligning short-term trades with longer-term trends.

Screen 1: Use weekly charts to identify the long-term trend with trend-following indicators like MACD or moving averages.

Screen 2: Use daily charts to find entry points against the weekly trend. Look for pullbacks in uptrends or rallies in downtrends.

Screen 3: Use intraday charts to fine-tune entry and exit points. Enter when the daily chart gives a signal in the direction of the weekly trend.

This system helps avoid fighting the major trend while finding good entry points during temporary counter-trend moves.""",
            concepts=["triple_screen", "multiple_timeframes", "trend_analysis", "entry_timing", "pullbacks"]
        )
    ],
    "psychology": [
        BookContent(
            book_title="The New Trading for a Living",
            chapter="Chapter 2",
            section="Trading Psychology",
            content="""Successful trading requires mastering your emotions. Fear and greed are the two main emotions that destroy trading accounts. Fear prevents you from taking good trades, while greed makes you hold losing positions too long.

Keep a trading diary to track not just your trades, but your emotions and decision-making process. Review your diary regularly to identify patterns in your behavior that lead to losses.

The goal is to become emotionally neutral about individual trades. Each trade is just one in a series. Your edge comes from following your system consistently over many trades, not from being right on any particular trade.""",
            concepts=["trading_psychology", "fear_greed", "trading_diary", "emotional_neutrality", "consistency"]
        )
    ]
}

# Options Trading for Beginners - Comprehensive Guide
OPTIONS_TRADING_CONTENT = {
    "basics": [
        BookContent(
            book_title="Options Trading for Beginners",
            chapter="Chapter 1",
            section="Options Fundamentals",
            content="""Options are contracts that give you the right, but not the obligation, to buy or sell a stock at a specific price (strike price) before a certain date (expiration). This fundamental concept is crucial for understanding all options strategies.

CALL OPTIONS - The Right to Buy:
A call option gives you the right to buy 100 shares of stock at the strike price. You profit when the stock price goes above your strike price plus the premium paid.

Example: AAPL $150 Call for $3.00
- You pay $300 for the right to buy 100 AAPL shares at $150
- Breakeven: $153 ($150 strike + $3 premium)
- Profit if AAPL > $153 at expiration

PUT OPTIONS - The Right to Sell:
A put option gives you the right to sell 100 shares at the strike price. You profit when the stock price goes below your strike price minus the premium paid.

Example: AAPL $150 Put for $2.50
- You pay $250 for the right to sell 100 AAPL shares at $150
- Breakeven: $147.50 ($150 strike - $2.50 premium)
- Profit if AAPL < $147.50 at expiration

CRITICAL SUCCESS FACTORS:
1. Direction: Must be right about stock movement
2. Magnitude: Move must be large enough to overcome premium
3. Timing: Must happen before expiration
4. Volatility: Higher volatility = higher premiums""",
            concepts=["options_basics", "calls", "puts", "strike_price", "expiration", "premium", "breakeven", "volatility"]
        ),
        BookContent(
            book_title="Options Trading for Beginners",
            chapter="Chapter 2",
            section="The Greeks - Risk Management",
            content="""The Greeks measure how option prices change with different market conditions. Understanding them is essential for managing risk and maximizing profits.

DELTA - Price Sensitivity:
Delta measures how much an option's price changes for each $1 move in the stock.
- Call deltas: 0 to 1.00 (ATM calls ≈ 0.50)
- Put deltas: -1.00 to 0 (ATM puts ≈ -0.50)
- Higher delta = more sensitive to stock price moves

GAMMA - Delta's Rate of Change:
Gamma shows how fast delta changes as the stock moves.
- Highest for at-the-money options
- Accelerates profits and losses near expiration
- Critical for position sizing

THETA - Time Decay:
Theta measures daily time decay of option value.
- Always negative for long options
- Accelerates as expiration approaches
- ATM options have highest theta

VEGA - Volatility Sensitivity:
Vega measures price change per 1% volatility change.
- Long options have positive vega
- Higher for ATM options and longer expirations
- Volatility crush after earnings can devastate long positions

IMPLIED VOLATILITY (IV):
IV represents the market's expectation of future volatility.
- High IV = expensive options
- Low IV = cheap options
- IV Rank: Compare current IV to 52-week range
- Buy low IV, sell high IV

PRACTICAL APPLICATIONS:
- Use delta for position sizing (10 delta ≈ 10 shares)
- Monitor gamma for acceleration risk
- Avoid high theta positions near expiration
- Check IV rank before entering trades""",
            concepts=["greeks", "delta", "gamma", "theta", "vega", "implied_volatility", "time_decay", "volatility_crush", "iv_rank"]
        )
    ],
    "strategies": [
        BookContent(
            book_title="Options Trading for Beginners",
            chapter="Chapter 3",
            section="Credit Spreads - Income Generation",
            content="""Credit spreads are the foundation of income-generating options strategies. You collect premium upfront and profit if the stock stays within your profit zone.

BULL PUT SPREAD (Bullish Income Strategy):
Sell a put at higher strike, buy a put at lower strike.

Example: AAPL at $155
- Sell $150 Put for $2.00
- Buy $145 Put for $0.75
- Net Credit: $1.25 ($125 per contract)
- Max Profit: $125 (if AAPL stays above $150)
- Max Loss: $375 (if AAPL below $145)
- Breakeven: $148.75 ($150 - $1.25 credit)

BEAR CALL SPREAD (Bearish Income Strategy):
Sell a call at lower strike, buy a call at higher strike.

Example: AAPL at $155
- Sell $160 Call for $1.50
- Buy $165 Call for $0.50
- Net Credit: $1.00 ($100 per contract)
- Max Profit: $100 (if AAPL stays below $160)
- Max Loss: $400 (if AAPL above $165)
- Breakeven: $161 ($160 + $1.00 credit)

CREDIT SPREAD MANAGEMENT RULES:
1. Target 1-2% return per month
2. Use 15-45 DTE (Days to Expiration)
3. Sell 15-20 delta options
4. Close at 25-50% max profit
5. Never hold through earnings
6. Size positions for 2-5% portfolio risk

IDEAL MARKET CONDITIONS:
- High implied volatility (IV Rank > 50)
- Trending or sideways markets
- Avoid before major events
- Best in stable, mature stocks""",
            concepts=["credit_spreads", "bull_put_spread", "bear_call_spread", "income_generation", "delta_selling", "dte", "iv_rank", "profit_management"]
        ),
        BookContent(
            book_title="Options Trading for Beginners",
            chapter="Chapter 4",
            section="Iron Condors - Range Trading",
            content="""Iron Condors are neutral strategies that profit when stocks trade sideways. Combine a bull put spread and bear call spread for defined risk income.

IRON CONDOR CONSTRUCTION:
1. Sell Bull Put Spread (lower strikes)
2. Sell Bear Call Spread (upper strikes)
3. Collect premium from both sides
4. Profit if stock stays between short strikes

Example: AAPL at $155 (45 DTE)
PUT SIDE:
- Sell $145 Put for $1.50
- Buy $140 Put for $0.75
- Put Credit: $0.75

CALL SIDE:
- Sell $165 Call for $1.25
- Buy $170 Call for $0.50
- Call Credit: $0.75

TOTAL CREDIT: $1.50 ($150 per contract)
- Max Profit: $150 (AAPL between $145-$165)
- Max Loss: $350 (AAPL below $140 or above $170)
- Profit Zone: $146.50 to $163.50
- Win Rate: ~70-80% with proper management

IRON CONDOR MANAGEMENT:
1. Close at 25% max profit
2. Defend tested side at 1x credit received
3. Roll untested side for additional credit
4. Never hold to expiration
5. Target wide profit zones (20+ points)

MARKET CONDITIONS FOR SUCCESS:
- Low volatility environments
- Stocks in consolidation
- After volatility crush events
- Avoid earnings and major events
- Best in large-cap, liquid stocks

ADVANCED TECHNIQUES:
- Unbalanced condors (bias one side)
- Rolling for duration and credit
- Profit-taking ladders
- Volatility timing entries""",
            concepts=["iron_condor", "neutral_strategy", "range_trading", "profit_zone", "tested_side", "rolling", "volatility_crush", "consolidation"]
        )
    ]
}

# Mastering the Trade - John Carter (TTM Squeeze Advanced)
MASTERING_THE_TRADE_CONTENT = {
    "ttm_squeeze": [
        BookContent(
            book_title="Mastering the Trade",
            chapter="Chapter 5",
            section="TTM Squeeze Advanced Techniques",
            content="""The TTM Squeeze is a volatility contraction pattern that predicts explosive moves. When Bollinger Bands contract inside Keltner Channels, volatility is compressed and ready to expand.

SQUEEZE MECHANICS:
The squeeze occurs when:
1. Bollinger Bands (20, 2.0) contract inside Keltner Channels (20, 1.5)
2. Volatility drops to extremely low levels
3. Price consolidates in tight range
4. Volume often decreases during compression

MOMENTUM OSCILLATOR:
The momentum line shows the direction of the coming breakout:
- Green dots: Upward momentum building
- Red dots: Downward momentum building
- Crossing zero line: Momentum shift confirmation
- Histogram height: Strength of momentum

ADVANCED ENTRY TECHNIQUES:

PRE-SQUEEZE SETUP:
Enter before the squeeze fires for maximum profit:
1. Identify squeeze forming (bands approaching channels)
2. Watch for momentum line direction
3. Enter on first momentum bar in trend direction
4. Use tight stops below/above squeeze range

POST-SQUEEZE BREAKOUT:
Enter after squeeze fires with confirmation:
1. Wait for bands to expand outside channels
2. Confirm with momentum line direction
3. Enter on first pullback to squeeze range
4. Target measured moves (squeeze range × 2)

SQUEEZE VARIATIONS:

BABY SQUEEZE:
- Shorter duration (5-10 bars)
- Smaller moves but higher probability
- Good for scalping and day trading
- Use smaller position sizes

MONSTER SQUEEZE:
- Extended duration (20+ bars)
- Massive moves when released
- Weekly/monthly timeframes
- Swing trading opportunities

FAILED SQUEEZE:
- Breaks one direction then reverses
- Often leads to bigger move opposite direction
- Watch for momentum divergence
- Excellent reversal signals""",
            concepts=["ttm_squeeze", "volatility_contraction", "bollinger_bands", "keltner_channels", "momentum_oscillator", "pre_squeeze", "post_squeeze", "baby_squeeze", "monster_squeeze", "failed_squeeze"]
        ),
        BookContent(
            book_title="Mastering the Trade",
            chapter="Chapter 6",
            section="Multi-Timeframe Squeeze Analysis",
            content="""Professional traders use multiple timeframes to identify the highest probability squeeze setups. Aligning squeezes across timeframes dramatically increases success rates.

TIMEFRAME HIERARCHY:
1. Monthly: Major trend direction
2. Weekly: Intermediate trend and major squeezes
3. Daily: Primary trading timeframe
4. 4-Hour: Entry refinement
5. 1-Hour: Precise entry timing

SQUEEZE ALIGNMENT STRATEGY:

TRIPLE SQUEEZE SETUP:
When squeezes align on 3 timeframes:
- Monthly squeeze: Major trend change coming
- Weekly squeeze: Intermediate move setup
- Daily squeeze: Immediate entry opportunity
- Success rate: 80-90%
- Risk/reward: Often 1:5 or better

ENTRY PROTOCOL:
1. Identify monthly trend direction
2. Confirm weekly squeeze in same direction
3. Wait for daily squeeze to fire
4. Enter on 4-hour pullback
5. Use 1-hour for precise timing

POSITION SIZING:
- Triple alignment: 3-5% risk
- Double alignment: 2-3% risk
- Single timeframe: 1-2% risk
- No alignment: Avoid trade

SQUEEZE MOMENTUM DIVERGENCE:
Watch for momentum divergence between timeframes:
- Higher timeframe momentum weakening
- Lower timeframe showing reversal signs
- Often signals major trend changes
- Excellent reversal trade setups

PRACTICAL EXAMPLES:

WEEKLY/DAILY ALIGNMENT:
AAPL weekly squeeze building, daily fires upward:
- Entry: Daily breakout above squeeze
- Stop: Below weekly squeeze low
- Target: Weekly measured move
- Hold time: 2-4 weeks

DAILY/4-HOUR SCALP:
TSLA daily squeeze, 4-hour fires:
- Entry: 4-hour momentum confirmation
- Stop: Below 4-hour squeeze
- Target: Daily resistance
- Hold time: 1-3 days

SQUEEZE FAILURE SIGNALS:
- Momentum divergence between timeframes
- Volume declining on breakout
- Immediate reversal after fire
- Often leads to opposite direction move""",
            concepts=["multi_timeframe", "squeeze_alignment", "triple_squeeze", "timeframe_hierarchy", "momentum_divergence", "position_sizing", "measured_moves", "squeeze_failure"]
        )
    ],
    "day_trading": [
        BookContent(
            book_title="Mastering the Trade",
            chapter="Chapter 8",
            section="Professional Day Trading Setups",
            content="""Successful day trading requires precise entry and exit techniques combined with strict risk management. Focus on high-probability setups with defined risk/reward ratios.

OPENING RANGE BREAKOUT (ORB):
Trade breakouts from the first 15-30 minutes of trading.

SETUP CRITERIA:
1. Identify opening range (first 15-30 min high/low)
2. Wait for breakout with volume
3. Enter on first pullback to range
4. Stop below/above opening range
5. Target 2-3x opening range size

EXAMPLE: AAPL Opening Range
- 9:30-10:00 AM range: $154.20 - $155.80
- Range size: $1.60
- Long entry: Break above $155.80
- Stop loss: $154.00 (below range)
- Target 1: $157.40 (1x range)
- Target 2: $159.00 (2x range)

GAP TRADING STRATEGIES:

GAP FILL TRADE:
When stocks gap up/down, they often fill the gap.
1. Identify significant gap (>1% on volume)
2. Wait for initial move to stall
3. Enter on reversal signals
4. Target gap fill level
5. Stop beyond day's high/low

GAP CONTINUATION:
Strong gaps often continue in gap direction.
1. Gap must be on significant news/volume
2. Wait for first pullback
3. Enter on resumption of gap direction
4. Use gap level as support/resistance
5. Target measured moves

MOMENTUM SCALPING:

5-MINUTE SQUEEZE SCALPS:
1. Identify 5-min TTM squeeze
2. Wait for momentum confirmation
3. Enter on first momentum bar
4. Target squeeze range × 1.5
5. Stop at squeeze opposite extreme

VWAP TRADING:
Volume Weighted Average Price as dynamic support/resistance:
- Above VWAP = bullish bias
- Below VWAP = bearish bias
- Bounces off VWAP = entry signals
- VWAP reclaim/loss = trend change

RISK MANAGEMENT RULES:
1. Never risk more than 1% per trade
2. 3:1 minimum risk/reward ratio
3. Maximum 3 losing trades per day
4. Stop trading after 2% daily loss
5. Take profits at predetermined levels""",
            concepts=["day_trading", "opening_range_breakout", "orb", "gap_trading", "gap_fill", "gap_continuation", "momentum_scalping", "vwap", "risk_management", "scalping"]
        )
    ]
}

# Advanced Technical Analysis - Professional Patterns
ADVANCED_TECHNICAL_ANALYSIS_CONTENT = {
    "patterns": [
        BookContent(
            book_title="Advanced Technical Analysis",
            chapter="Chapter 1",
            section="Institutional Order Flow Patterns",
            content="""Professional traders recognize institutional order flow through specific chart patterns that retail traders often miss. These patterns reveal where big money is accumulating or distributing.

WYCKOFF ACCUMULATION:
Institutions accumulate positions during sideways consolidation phases.

PHASE A - SELLING CLIMAX:
- High volume selling exhaustion
- Preliminary support (PS) forms
- Selling climax (SC) on massive volume
- Automatic rally (AR) follows
- Secondary test (ST) of lows

PHASE B - BUILDING CAUSE:
- Extended sideways movement
- Multiple tests of support/resistance
- Decreasing volume on declines
- Spring (false breakdown) shakes out weak hands
- Last point of support (LPS) holds

PHASE C - MARKUP PREPARATION:
- Sign of strength (SOS) breakout
- Last point of support (LPS) backtest
- Volume expansion on rallies
- No supply on pullbacks

PHASE D - MARKUP:
- Sustained uptrend begins
- Higher highs and higher lows
- Strong volume on advances
- Weak volume on declines

PRACTICAL IDENTIFICATION:
1. Look for 3-6 month consolidation
2. Identify volume patterns
3. Watch for spring/upthrust tests
4. Confirm with relative strength
5. Enter on LPS or SOS breakout

DISTRIBUTION PATTERNS:
Mirror image of accumulation:
- Buying climax instead of selling climax
- Upthrust instead of spring
- Sign of weakness instead of strength
- Last point of supply instead of support

VOLUME ANALYSIS:
- Accumulation: Volume increases on rallies
- Distribution: Volume increases on declines
- Climax volume: Exhaustion signals
- Dry up volume: Lack of interest""",
            concepts=["wyckoff", "accumulation", "distribution", "institutional_flow", "selling_climax", "spring", "sign_of_strength", "last_point_support", "volume_analysis"]
        ),
        BookContent(
            book_title="Advanced Technical Analysis",
            chapter="Chapter 2",
            section="Market Structure and Smart Money Concepts",
            content="""Understanding market structure helps identify where institutional money is positioned and likely to act. Smart money concepts reveal the true supply and demand dynamics.

MARKET STRUCTURE BASICS:

HIGHER HIGHS/HIGHER LOWS (Uptrend):
- Each swing high exceeds previous high
- Each swing low exceeds previous low
- Trend intact until structure breaks
- Look for continuation patterns

LOWER HIGHS/LOWER LOWS (Downtrend):
- Each swing high below previous high
- Each swing low below previous low
- Trend intact until structure breaks
- Look for distribution patterns

BREAK OF STRUCTURE (BOS):
- Uptrend: Lower low formation
- Downtrend: Higher high formation
- Signals potential trend change
- Wait for confirmation

CHANGE OF CHARACTER (CHoCH):
- Momentum shift in price action
- Volume patterns change
- Often precedes structure break
- Early warning signal

SMART MONEY CONCEPTS:

ORDER BLOCKS:
Areas where institutions placed large orders:
- Last bullish candle before decline
- Last bearish candle before rally
- Often act as future support/resistance
- High probability reversal zones

FAIR VALUE GAPS (FVG):
Imbalances in price action:
- Three candle pattern with gap
- Middle candle doesn't overlap others
- Often filled by future price action
- Magnetic effect on price

LIQUIDITY POOLS:
Areas where stops are clustered:
- Above swing highs (buy stops)
- Below swing lows (sell stops)
- Equal highs/lows (double tops/bottoms)
- Institutions hunt these levels

INDUCEMENT:
False moves to trigger retail stops:
- Break above resistance to trigger buys
- Immediately reverse lower
- Break below support to trigger sells
- Immediately reverse higher

PRACTICAL APPLICATION:
1. Identify current market structure
2. Mark key order blocks and FVGs
3. Locate liquidity pools
4. Watch for inducement moves
5. Enter on structure confirmation""",
            concepts=["market_structure", "break_of_structure", "change_of_character", "order_blocks", "fair_value_gaps", "liquidity_pools", "inducement", "smart_money"]
        )
    ],
    "quantitative": [
        BookContent(
            book_title="Advanced Technical Analysis",
            chapter="Chapter 3",
            section="Quantitative Trading Strategies",
            content="""Quantitative strategies use mathematical models and statistical analysis to identify trading opportunities. These systematic approaches remove emotion and provide consistent edge.

MEAN REVERSION STRATEGIES:

BOLLINGER BAND MEAN REVERSION:
When price extends beyond 2 standard deviations, it tends to revert to the mean.

SETUP:
1. Price touches or exceeds upper/lower Bollinger Band
2. RSI shows overbought/oversold (>70 or <30)
3. Volume confirms the extreme move
4. Enter on first reversal signal
5. Target middle Bollinger Band (20 SMA)

STATISTICAL PARAMETERS:
- Win rate: 65-75%
- Average hold time: 3-7 days
- Risk/reward: 1:1.5 to 1:2
- Best in ranging markets

PAIRS TRADING:
Trade the relative performance between correlated stocks.

METHODOLOGY:
1. Identify highly correlated pairs (>0.8 correlation)
2. Calculate historical spread ratio
3. Enter when spread exceeds 2 standard deviations
4. Long underperformer, short outperformer
5. Exit when spread returns to mean

EXAMPLE: AAPL vs MSFT
- Normal ratio: 1.15 (AAPL/MSFT)
- Current ratio: 1.25 (2 std dev above)
- Trade: Short AAPL, Long MSFT
- Target: Ratio return to 1.15
- Stop: Ratio exceeds 1.30

MOMENTUM STRATEGIES:

RELATIVE STRENGTH MOMENTUM:
Buy stocks showing relative strength vs market.

CALCULATION:
RS = (Stock Price / Market Price) / (Stock Price 20 days ago / Market Price 20 days ago)

CRITERIA:
- RS > 1.05 (outperforming market by 5%)
- Stock above 50-day moving average
- Market in uptrend
- Volume above average
- Enter on pullback to support

BREAKOUT MOMENTUM:
Systematic approach to trading breakouts.

FILTERS:
1. Consolidation period: 20+ days
2. Volatility contraction: ATR declining
3. Volume pattern: Decreasing in range
4. Breakout volume: 150%+ of average
5. Follow-through: Second day confirmation

RISK MANAGEMENT:
- Position size: 1% risk per trade
- Stop loss: Below consolidation range
- Profit target: 2x consolidation range
- Time stop: Exit if no progress in 5 days

BACKTESTING REQUIREMENTS:
- Minimum 2 years of data
- Include transaction costs
- Account for slippage
- Test across different market conditions
- Validate with out-of-sample data""",
            concepts=["quantitative_trading", "mean_reversion", "bollinger_bands", "pairs_trading", "correlation", "relative_strength", "momentum", "breakout_systems", "backtesting", "statistical_analysis"]
        )
    ]
}

# Consolidated book content dictionary
ALL_BOOK_CONTENT = {
    "trading_in_the_zone": TRADING_IN_THE_ZONE_CONTENT,
    "market_wizards": MARKET_WIZARDS_CONTENT,
    "technical_analysis_explained": TECHNICAL_ANALYSIS_EXPLAINED_CONTENT,
    "how_to_make_money_in_stocks": HOW_TO_MAKE_MONEY_CONTENT,
    "new_trading_for_living": NEW_TRADING_FOR_LIVING_CONTENT,
    "options_trading": OPTIONS_TRADING_CONTENT,
    "mastering_the_trade": MASTERING_THE_TRADE_CONTENT,
    "advanced_technical_analysis": ADVANCED_TECHNICAL_ANALYSIS_CONTENT
}

def get_all_book_content() -> List[BookContent]:
    """Get all book content as a flat list for embedding"""
    all_content = []
    
    for book_key, book_data in ALL_BOOK_CONTENT.items():
        for section_key, section_content in book_data.items():
            if isinstance(section_content, list):
                all_content.extend(section_content)
            else:
                all_content.append(section_content)
    
    return all_content

def search_book_content(query: str, book_filter: str = None) -> List[BookContent]:
    """Search book content by keywords"""
    query_lower = query.lower()
    results = []
    
    for book_key, book_data in ALL_BOOK_CONTENT.items():
        if book_filter and book_filter.lower() not in book_key.lower():
            continue
            
        for section_key, section_content in book_data.items():
            if isinstance(section_content, list):
                for content in section_content:
                    if (query_lower in content.content.lower() or 
                        any(concept.lower() in query_lower for concept in content.concepts)):
                        results.append(content)
    
    return results
