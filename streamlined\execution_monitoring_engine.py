"""
A.T.L.A.S Real-Time Execution and Monitoring Engine
Live trade execution with automatic stop-loss and intelligent fallback mechanisms
"""

import asyncio
import logging
from datetime import datetime, timedelta, time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from config import settings
from models import Order, Position, OrderSide, OrderType, OrderStatus
from risk_management_engine import PositionSizeCalculation, PreTradeValidation


class MarketCondition(Enum):
    NORMAL = "normal"
    VOLATILE = "volatile"
    HALTED = "halted"
    CLOSED = "closed"


@dataclass
class LiveTradeExecution:
    """Live trade execution result"""
    order_id: str
    symbol: str
    status: str
    entry_price: float
    stop_loss_order_id: Optional[str] = None
    target_order_id: Optional[str] = None
    execution_notes: List[str] = None
    educational_explanation: str = ""


@dataclass
class PortfolioMonitoring:
    """Real-time portfolio monitoring"""
    total_value: float
    daily_pnl: float
    daily_pnl_percent: float
    open_positions: int
    daily_risk_used: float
    risk_limit_remaining: float
    market_condition: MarketCondition
    alerts: List[str]
    performance_summary: str


@dataclass
class IntelligentFallback:
    """Intelligent fallback decision"""
    original_strategy: str
    fallback_action: str
    reasoning: str
    new_parameters: Dict[str, Any]
    educational_note: str


class ExecutionMonitoringEngine:
    """
    Real-time execution and monitoring system with educational guidance
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Trading hours (EST)
        self.market_open = time(9, 30)
        self.market_close = time(16, 0)
        self.safe_trading_start = time(10, 0)  # Avoid opening volatility
        self.safe_trading_end = time(15, 30)   # Avoid closing volatility
        
        # Risk thresholds
        self.VIX_NORMAL = 20.0
        self.VIX_VOLATILE = 30.0
        self.VIX_DANGER = 40.0
        
        # Position monitoring
        self.active_positions: Dict[str, Dict] = {}
        self.daily_pnl_tracker = 0.0
        self.daily_risk_used = 0.0
        
    async def execute_trade_with_protection(self, 
                                          symbol: str,
                                          position_calc: PositionSizeCalculation,
                                          validation: PreTradeValidation,
                                          order_type: str = "market") -> LiveTradeExecution:
        """
        Execute trade with automatic stop-loss and target orders
        """
        
        execution_notes = []
        
        # Pre-execution checks
        if not validation.is_valid:
            return LiveTradeExecution(
                order_id="",
                symbol=symbol,
                status="REJECTED",
                entry_price=0.0,
                execution_notes=validation.blockers,
                educational_explanation="Trade rejected due to risk management rules"
            )
        
        # Market timing check
        market_condition = await self._check_market_conditions()
        if market_condition in [MarketCondition.HALTED, MarketCondition.CLOSED]:
            return LiveTradeExecution(
                order_id="",
                symbol=symbol,
                status="REJECTED",
                entry_price=0.0,
                execution_notes=[f"Market condition: {market_condition.value}"],
                educational_explanation="Trading suspended due to market conditions"
            )
        
        try:
            # Execute main order (simulated for now)
            main_order_id = await self._execute_main_order(
                symbol, position_calc.recommended_shares, order_type
            )
            
            if not main_order_id:
                return LiveTradeExecution(
                    order_id="",
                    symbol=symbol,
                    status="FAILED",
                    entry_price=0.0,
                    execution_notes=["Order execution failed"],
                    educational_explanation="Unable to execute order - check connectivity and account status"
                )
            
            # Get fill price (simulated)
            fill_price = await self._get_fill_price(main_order_id)
            
            # Place protective stop-loss order
            stop_loss_order_id = await self._place_stop_loss_order(
                symbol, position_calc.recommended_shares, position_calc.stop_loss_price
            )
            
            # Place target order
            target_order_id = await self._place_target_order(
                symbol, position_calc.recommended_shares, position_calc.target_price
            )
            
            # Update position tracking
            self._update_position_tracking(symbol, position_calc, fill_price)
            
            # Generate educational explanation
            educational_explanation = self._generate_execution_explanation(
                symbol, position_calc, fill_price, stop_loss_order_id, target_order_id
            )
            
            execution_notes.append(f"Order executed at ${fill_price:.2f}")
            if stop_loss_order_id:
                execution_notes.append(f"Stop-loss placed at ${position_calc.stop_loss_price:.2f}")
            if target_order_id:
                execution_notes.append(f"Target order placed at ${position_calc.target_price:.2f}")
            
            return LiveTradeExecution(
                order_id=main_order_id,
                symbol=symbol,
                status="FILLED",
                entry_price=fill_price,
                stop_loss_order_id=stop_loss_order_id,
                target_order_id=target_order_id,
                execution_notes=execution_notes,
                educational_explanation=educational_explanation
            )
            
        except Exception as e:
            self.logger.error(f"Error executing trade for {symbol}: {e}")
            return LiveTradeExecution(
                order_id="",
                symbol=symbol,
                status="ERROR",
                entry_price=0.0,
                execution_notes=[f"Execution error: {str(e)}"],
                educational_explanation="Technical error during execution - please try again"
            )
    
    async def monitor_portfolio_realtime(self, account_size: float) -> PortfolioMonitoring:
        """
        Real-time portfolio monitoring with alerts and risk management
        """
        
        alerts = []
        
        # Calculate current portfolio metrics (simulated)
        total_value = account_size + self.daily_pnl_tracker
        daily_pnl_percent = (self.daily_pnl_tracker / account_size) * 100
        
        # Check daily loss limits
        daily_loss_limit = account_size * 0.03  # 3% daily limit
        if self.daily_pnl_tracker < -daily_loss_limit:
            alerts.append("🚨 DAILY LOSS LIMIT EXCEEDED - Trading suspended")
        elif self.daily_pnl_tracker < -daily_loss_limit * 0.75:
            alerts.append("⚠️ Approaching daily loss limit (75% used)")
        
        # Check risk utilization
        risk_limit_remaining = daily_loss_limit - abs(min(self.daily_pnl_tracker, 0))
        
        # Market condition assessment
        market_condition = await self._check_market_conditions()
        if market_condition == MarketCondition.VOLATILE:
            alerts.append("🌪️ High volatility detected - reduce position sizes")
        elif market_condition == MarketCondition.HALTED:
            alerts.append("⏸️ Market halted - no new trades")
        
        # Time-based alerts
        current_time = datetime.now().time()
        if current_time > self.safe_trading_end:
            alerts.append("⏰ Approaching market close - avoid new positions")
        
        # Position monitoring
        open_positions = len(self.active_positions)
        if open_positions > 5:
            alerts.append("📊 High number of open positions - consider consolidation")
        
        # Generate performance summary
        performance_summary = self._generate_performance_summary(
            total_value, self.daily_pnl_tracker, daily_pnl_percent, open_positions
        )
        
        return PortfolioMonitoring(
            total_value=total_value,
            daily_pnl=self.daily_pnl_tracker,
            daily_pnl_percent=daily_pnl_percent,
            open_positions=open_positions,
            daily_risk_used=abs(min(self.daily_pnl_tracker, 0)),
            risk_limit_remaining=risk_limit_remaining,
            market_condition=market_condition,
            alerts=alerts,
            performance_summary=performance_summary
        )
    
    async def intelligent_fallback_decision(self, 
                                          symbol: str,
                                          original_strategy: str,
                                          current_conditions: Dict[str, Any]) -> IntelligentFallback:
        """
        Make intelligent fallback decisions when conditions change
        """
        
        # Analyze current conditions
        signal_strength = current_conditions.get("confidence", 0.5)
        market_volatility = current_conditions.get("vix", 20.0)
        time_remaining = current_conditions.get("time_to_close_hours", 4.0)
        
        # Decision logic
        if signal_strength < 0.5:
            fallback_action = "reduce_position_size"
            reasoning = f"Signal confidence dropped to {signal_strength*100:.0f}% - reducing position size by 50%"
            new_parameters = {"position_multiplier": 0.5}
            educational_note = "When signal quality deteriorates, reducing position size helps manage risk"
            
        elif market_volatility > self.VIX_DANGER:
            fallback_action = "pause_trading"
            reasoning = f"Market volatility too high (VIX: {market_volatility:.1f}) - pausing new trades"
            new_parameters = {"trading_suspended": True}
            educational_note = "High volatility periods require extra caution - better to wait for calmer markets"
            
        elif time_remaining < 0.5:  # Less than 30 minutes to close
            fallback_action = "convert_to_swing"
            reasoning = "Too close to market close for day trading - converting to swing trade strategy"
            new_parameters = {"timeframe": "swing", "stop_loss_multiplier": 1.5}
            educational_note = "Day trades near market close carry extra risk - swing trades allow overnight holding"
            
        else:
            fallback_action = "continue"
            reasoning = "Conditions remain favorable for original strategy"
            new_parameters = {}
            educational_note = "All systems green - proceeding with original plan"
        
        return IntelligentFallback(
            original_strategy=original_strategy,
            fallback_action=fallback_action,
            reasoning=reasoning,
            new_parameters=new_parameters,
            educational_note=educational_note
        )
    
    async def _check_market_conditions(self) -> MarketCondition:
        """Check current market conditions"""
        
        current_time = datetime.now().time()
        
        # Check if market is open
        if current_time < self.market_open or current_time > self.market_close:
            return MarketCondition.CLOSED
        
        # Simulate VIX check (would use real data)
        simulated_vix = 25.0  # Placeholder
        
        if simulated_vix > self.VIX_DANGER:
            return MarketCondition.HALTED
        elif simulated_vix > self.VIX_VOLATILE:
            return MarketCondition.VOLATILE
        else:
            return MarketCondition.NORMAL
    
    async def _execute_main_order(self, symbol: str, shares: int, order_type: str) -> Optional[str]:
        """Execute main order (simulated)"""
        # This would integrate with Alpaca API
        # For now, return simulated order ID
        order_id = f"ORDER_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.logger.info(f"Simulated order execution: {shares} shares of {symbol}")
        return order_id
    
    async def _get_fill_price(self, order_id: str) -> float:
        """Get order fill price (simulated)"""
        # This would query actual fill price from broker
        # For now, return simulated price
        return 100.0  # Placeholder
    
    async def _place_stop_loss_order(self, symbol: str, shares: int, stop_price: float) -> Optional[str]:
        """Place stop-loss order (simulated)"""
        stop_order_id = f"STOP_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.logger.info(f"Simulated stop-loss order: {shares} shares of {symbol} at ${stop_price:.2f}")
        return stop_order_id
    
    async def _place_target_order(self, symbol: str, shares: int, target_price: float) -> Optional[str]:
        """Place target order (simulated)"""
        target_order_id = f"TARGET_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.logger.info(f"Simulated target order: {shares} shares of {symbol} at ${target_price:.2f}")
        return target_order_id
    
    def _update_position_tracking(self, symbol: str, position_calc: PositionSizeCalculation, fill_price: float):
        """Update internal position tracking"""
        self.active_positions[symbol] = {
            "shares": position_calc.recommended_shares,
            "entry_price": fill_price,
            "stop_loss": position_calc.stop_loss_price,
            "target": position_calc.target_price,
            "risk_amount": position_calc.risk_amount,
            "entry_time": datetime.now()
        }
        
        self.daily_risk_used += position_calc.risk_amount
    
    def _generate_execution_explanation(self, symbol: str, position_calc: PositionSizeCalculation,
                                      fill_price: float, stop_order_id: Optional[str],
                                      target_order_id: Optional[str]) -> str:
        """Generate educational explanation of execution"""
        
        explanation = f"""
🎯 TRADE EXECUTION COMPLETE FOR {symbol}

📊 WHAT JUST HAPPENED:
• Bought {position_calc.recommended_shares} shares at ${fill_price:.2f}
• Total investment: ${position_calc.recommended_shares * fill_price:,.2f}
• Risk amount: ${position_calc.risk_amount:.2f}

🛡️ AUTOMATIC PROTECTION ORDERS:
• Stop-loss at ${position_calc.stop_loss_price:.2f} (limits loss to ${position_calc.risk_amount:.2f})
• Target at ${position_calc.target_price:.2f} (potential profit: ${(position_calc.target_price - fill_price) * position_calc.recommended_shares:.2f})

💡 BEGINNER EXPLANATION:
We just bought shares of {symbol} and immediately placed two protective orders:
1. Stop-loss: Automatically sells if price drops to ${position_calc.stop_loss_price:.2f} (protects you from big losses)
2. Target: Automatically sells if price rises to ${position_calc.target_price:.2f} (locks in profits)

This is called "bracket trading" - like having automatic bodyguards for your money!

📈 WHAT HAPPENS NEXT:
The system will monitor this position 24/7. You don't need to watch the screen constantly.
If the trade goes well, it will automatically take profits. If it goes badly, it will automatically limit losses.
        """.strip()
        
        return explanation
    
    def _generate_performance_summary(self, total_value: float, daily_pnl: float,
                                    daily_pnl_percent: float, open_positions: int) -> str:
        """Generate performance summary"""
        
        status_emoji = "📈" if daily_pnl >= 0 else "📉"
        
        summary = f"""
{status_emoji} TODAY'S PERFORMANCE SUMMARY

💰 Account Value: ${total_value:,.2f}
📊 Daily P&L: ${daily_pnl:+,.2f} ({daily_pnl_percent:+.2f}%)
🎯 Open Positions: {open_positions}

💡 PERFORMANCE INSIGHT:
{"Great job! You're having a profitable day. Remember to stick to your risk management rules." if daily_pnl > 0 else "Don't worry about red days - they're part of trading. Your risk management is protecting you."}

🎯 NEXT STEPS:
{"Consider taking some profits if you're up significantly" if daily_pnl_percent > 2 else "Stay disciplined and wait for high-quality setups"}
        """.strip()
        
        return summary
