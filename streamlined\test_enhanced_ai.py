"""
Test script for enhanced A.T.L.A.S conversational intelligence
"""

import asyncio
import logging
from datetime import datetime

from atlas_ai_engine import (
    AtlasAIEngine, EmotionalIntelligence, GoalParser, 
    ConversationalFlowEngine, GroundingEngine, EnhancedContextMemory
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_emotional_intelligence():
    """Test emotional intelligence and tone analysis"""
    
    print("🧠 Testing Emotional Intelligence...")
    
    emotional_ai = EmotionalIntelligence()
    
    test_messages = [
        "I need to make back the $500 I lost yesterday!",
        "I'm worried about this trade, seems risky",
        "I'm confident this will work, let's go big!",
        "This is so frustrating, nothing works!",
        "I want to make $100 today before market close",
        "Can you explain how RSI works?"
    ]
    
    for message in test_messages:
        emotional_state = emotional_ai.analyze_emotional_state(message)
        print(f"\nMessage: '{message}'")
        print(f"Emotion: {emotional_state.primary_emotion}")
        print(f"Confidence: {emotional_state.confidence_level:.2f}")
        print(f"Risk Seeking: {emotional_state.risk_seeking:.2f}")
        print(f"Urgency: {emotional_state.urgency_level:.2f}")
        if emotional_state.warning_flags:
            print(f"⚠️ Warnings: {emotional_state.warning_flags}")

async def test_goal_parsing():
    """Test goal parsing and intent recognition"""
    
    print("\n🎯 Testing Goal Parsing...")
    
    goal_parser = GoalParser()
    
    test_messages = [
        "I want to make $50 today",
        "Help me earn $200 this week",
        "I need to learn about options",
        "Can you test a momentum strategy?",
        "I don't want to risk more than $100",
        "Make me money however you can"
    ]
    
    for message in test_messages:
        goal = goal_parser.parse_trading_goal(message, "test_user")
        print(f"\nMessage: '{message}'")
        if goal:
            print(f"Goal Type: {goal.goal_type}")
            print(f"Target Amount: ${goal.target_amount}" if goal.target_amount else "No amount specified")
            print(f"Timeframe: {goal.timeframe}" if goal.timeframe else "No timeframe specified")
        else:
            print("No clear goal detected")

async def test_conversational_flow():
    """Test conversational flow and clarification"""
    
    print("\n💬 Testing Conversational Flow...")
    
    flow_engine = ConversationalFlowEngine()
    
    test_messages = [
        "Make me money",
        "What's the best stock?",
        "AAPL",
        "Find me something safe",
        "I want quick profit"
    ]
    
    for message in test_messages:
        clarification = flow_engine.needs_clarification(message, {})
        print(f"\nMessage: '{message}'")
        if clarification:
            print(f"Clarification needed: {clarification}")
        else:
            print("No clarification needed")

async def test_enhanced_memory():
    """Test enhanced context memory system"""
    
    print("\n🧠 Testing Enhanced Memory System...")
    
    memory = EnhancedContextMemory()
    
    # Test storing and retrieving goals
    from atlas_ai_engine import TradingGoal
    
    goal = TradingGoal(
        goal_type="profit_target",
        target_amount=100.0,
        timeframe="today",
        risk_tolerance="moderate"
    )
    
    goal_id = memory.store_trading_goal("test_user", goal)
    print(f"Stored goal with ID: {goal_id}")
    
    retrieved_goal = memory.get_active_goal("test_user")
    if retrieved_goal:
        print(f"Retrieved goal: {retrieved_goal.goal_type} - ${retrieved_goal.target_amount}")
    
    # Test rejected signals
    memory.store_rejected_signal("test_user", "TTM_SQUEEZE", "AAPL", "Too risky for current market")
    print("Stored rejected signal")

async def test_grounding_engine():
    """Test grounding and fact-checking"""
    
    print("\n📚 Testing Grounding Engine...")
    
    grounding = GroundingEngine()
    
    test_responses = [
        "Risk management is crucial for trading success.",
        "Trading psychology affects your decisions.",
        "Always cut your losses quickly."
    ]
    
    for response in test_responses:
        grounded = grounding.ground_response(response, "risk_management")
        print(f"\nOriginal: {response}")
        print(f"Grounded: {grounded}")

async def test_full_ai_pipeline():
    """Test the complete enhanced AI pipeline"""
    
    print("\n🚀 Testing Full Enhanced AI Pipeline...")
    
    ai_engine = AtlasAIEngine()
    
    test_scenarios = [
        {
            "message": "I need to make back $500 I lost yesterday!",
            "context": {"session_id": "test_session", "user_id": "test_user"}
        },
        {
            "message": "What's a good stock to buy?",
            "context": {"session_id": "test_session", "user_id": "test_user"}
        },
        {
            "message": "I want to make $100 today",
            "context": {"session_id": "test_session", "user_id": "test_user"}
        },
        {
            "message": "Explain RSI to me",
            "context": {"session_id": "test_session", "user_id": "test_user"}
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n--- Scenario {i} ---")
        print(f"User: {scenario['message']}")
        
        try:
            response = await ai_engine.process_trading_query(
                scenario['message'], 
                scenario['context']
            )
            
            print(f"A.T.L.A.S: {response.response}")
            print(f"Type: {response.type}")
            print(f"Confidence: {response.confidence}")
            
            if response.emotional_state:
                print(f"Detected Emotion: {response.emotional_state}")
            
            if response.active_goal:
                print(f"Active Goal: {response.active_goal}")
                
        except Exception as e:
            print(f"Error: {e}")

async def main():
    """Run all tests"""
    
    print("🧪 Testing A.T.L.A.S Enhanced Conversational Intelligence\n")
    print("=" * 60)
    
    try:
        await test_emotional_intelligence()
        await test_goal_parsing()
        await test_conversational_flow()
        await test_enhanced_memory()
        await test_grounding_engine()
        await test_full_ai_pipeline()
        
        print("\n" + "=" * 60)
        print("✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
