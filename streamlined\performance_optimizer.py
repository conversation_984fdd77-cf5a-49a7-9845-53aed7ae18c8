"""
A.T.L.A.S Performance Optimization System
Optimizes system performance for scalability and seamless feature integration
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
import threading
from concurrent.futures import ThreadPoolExecutor
import weakref

from .config import settings


@dataclass
class PerformanceMetrics:
    """Performance metrics tracking"""
    operation_name: str
    start_time: float
    end_time: float
    duration_ms: float
    memory_usage_mb: float
    success: bool
    error_message: Optional[str] = None


class CacheManager:
    """Intelligent caching system for A.T.L.A.S components"""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 300):
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.access_times: Dict[str, float] = {}
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.lock = threading.RLock()
        
        # Start cleanup thread
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
    
    def get(self, key: str) -> Optional[Any]:
        """Get cached value if valid"""
        with self.lock:
            if key not in self.cache:
                return None
            
            # Check TTL
            if time.time() - self.cache[key]["timestamp"] > self.ttl_seconds:
                del self.cache[key]
                del self.access_times[key]
                return None
            
            # Update access time
            self.access_times[key] = time.time()
            return self.cache[key]["value"]
    
    def set(self, key: str, value: Any) -> None:
        """Set cached value"""
        with self.lock:
            # Evict if at capacity
            if len(self.cache) >= self.max_size:
                self._evict_lru()
            
            self.cache[key] = {
                "value": value,
                "timestamp": time.time()
            }
            self.access_times[key] = time.time()
    
    def _evict_lru(self) -> None:
        """Evict least recently used item"""
        if not self.access_times:
            return
        
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        del self.cache[lru_key]
        del self.access_times[lru_key]
    
    def _cleanup_loop(self) -> None:
        """Background cleanup of expired entries"""
        while True:
            try:
                time.sleep(60)  # Check every minute
                current_time = time.time()
                
                with self.lock:
                    expired_keys = [
                        key for key, data in self.cache.items()
                        if current_time - data["timestamp"] > self.ttl_seconds
                    ]
                    
                    for key in expired_keys:
                        del self.cache[key]
                        if key in self.access_times:
                            del self.access_times[key]
                            
            except Exception:
                pass  # Continue cleanup loop even on errors


class PerformanceMonitor:
    """Monitor and track system performance"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.metrics: List[PerformanceMetrics] = []
        self.lock = threading.RLock()
        
        # Performance thresholds
        self.thresholds = {
            "response_time_ms": 5000,  # 5 seconds max
            "memory_usage_mb": 500,    # 500MB max
            "error_rate_percent": 5.0   # 5% max error rate
        }
    
    def start_operation(self, operation_name: str) -> str:
        """Start tracking an operation"""
        operation_id = f"{operation_name}_{int(time.time() * 1000)}"
        return operation_id
    
    def end_operation(self, operation_id: str, success: bool = True, 
                     error_message: Optional[str] = None) -> PerformanceMetrics:
        """End tracking an operation"""
        
        # Extract operation name and start time from ID
        parts = operation_id.rsplit("_", 1)
        operation_name = parts[0]
        start_time = float(parts[1]) / 1000
        
        end_time = time.time()
        duration_ms = (end_time - start_time) * 1000
        
        # Get memory usage (simplified)
        memory_usage_mb = self._get_memory_usage()
        
        metrics = PerformanceMetrics(
            operation_name=operation_name,
            start_time=start_time,
            end_time=end_time,
            duration_ms=duration_ms,
            memory_usage_mb=memory_usage_mb,
            success=success,
            error_message=error_message
        )
        
        with self.lock:
            self.metrics.append(metrics)
            
            # Keep only recent metrics (last 1000)
            if len(self.metrics) > 1000:
                self.metrics = self.metrics[-1000:]
        
        # Log performance warnings
        if duration_ms > self.thresholds["response_time_ms"]:
            self.logger.warning(f"Slow operation: {operation_name} took {duration_ms:.1f}ms")
        
        if memory_usage_mb > self.thresholds["memory_usage_mb"]:
            self.logger.warning(f"High memory usage: {memory_usage_mb:.1f}MB")
        
        return metrics
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            return 0.0  # psutil not available
    
    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get performance summary for the last N hours"""
        
        cutoff_time = time.time() - (hours * 3600)
        
        with self.lock:
            recent_metrics = [
                m for m in self.metrics 
                if m.start_time >= cutoff_time
            ]
        
        if not recent_metrics:
            return {"message": "No recent metrics available"}
        
        # Calculate statistics
        total_operations = len(recent_metrics)
        successful_operations = sum(1 for m in recent_metrics if m.success)
        error_rate = ((total_operations - successful_operations) / total_operations) * 100
        
        durations = [m.duration_ms for m in recent_metrics]
        avg_duration = sum(durations) / len(durations)
        max_duration = max(durations)
        
        memory_usages = [m.memory_usage_mb for m in recent_metrics]
        avg_memory = sum(memory_usages) / len(memory_usages)
        max_memory = max(memory_usages)
        
        # Operation breakdown
        operation_stats = {}
        for metric in recent_metrics:
            op_name = metric.operation_name
            if op_name not in operation_stats:
                operation_stats[op_name] = {
                    "count": 0,
                    "avg_duration": 0,
                    "success_rate": 0
                }
            
            operation_stats[op_name]["count"] += 1
            operation_stats[op_name]["avg_duration"] += metric.duration_ms
            if metric.success:
                operation_stats[op_name]["success_rate"] += 1
        
        # Finalize operation stats
        for op_name, stats in operation_stats.items():
            stats["avg_duration"] /= stats["count"]
            stats["success_rate"] = (stats["success_rate"] / stats["count"]) * 100
        
        return {
            "period_hours": hours,
            "total_operations": total_operations,
            "success_rate": ((successful_operations / total_operations) * 100),
            "error_rate": error_rate,
            "avg_response_time_ms": avg_duration,
            "max_response_time_ms": max_duration,
            "avg_memory_usage_mb": avg_memory,
            "max_memory_usage_mb": max_memory,
            "operation_breakdown": operation_stats,
            "performance_grade": self._calculate_performance_grade(error_rate, avg_duration, avg_memory)
        }
    
    def _calculate_performance_grade(self, error_rate: float, avg_duration: float, avg_memory: float) -> str:
        """Calculate overall performance grade"""
        
        score = 100
        
        # Deduct for high error rate
        if error_rate > 5:
            score -= (error_rate - 5) * 5
        
        # Deduct for slow response times
        if avg_duration > 1000:  # > 1 second
            score -= (avg_duration - 1000) / 100
        
        # Deduct for high memory usage
        if avg_memory > 200:  # > 200MB
            score -= (avg_memory - 200) / 10
        
        score = max(0, score)
        
        if score >= 90:
            return "EXCELLENT"
        elif score >= 75:
            return "GOOD"
        elif score >= 60:
            return "FAIR"
        else:
            return "NEEDS_IMPROVEMENT"


class AsyncTaskManager:
    """Manage async tasks for better performance"""
    
    def __init__(self, max_concurrent_tasks: int = 10):
        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)
        self.active_tasks: Dict[str, asyncio.Task] = {}
        self.task_results: Dict[str, Any] = {}
        
    async def run_task(self, task_id: str, coro: Callable) -> Any:
        """Run async task with concurrency control"""
        
        async with self.semaphore:
            try:
                task = asyncio.create_task(coro)
                self.active_tasks[task_id] = task
                
                result = await task
                self.task_results[task_id] = {"success": True, "result": result}
                return result
                
            except Exception as e:
                self.task_results[task_id] = {"success": False, "error": str(e)}
                raise
            finally:
                if task_id in self.active_tasks:
                    del self.active_tasks[task_id]
    
    def get_task_status(self) -> Dict[str, Any]:
        """Get status of all tasks"""
        return {
            "active_tasks": len(self.active_tasks),
            "completed_tasks": len(self.task_results),
            "task_details": {
                task_id: "running" for task_id in self.active_tasks.keys()
            }
        }


class PerformanceOptimizer:
    """Main performance optimization coordinator"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.cache_manager = CacheManager(max_size=1000, ttl_seconds=300)
        self.performance_monitor = PerformanceMonitor()
        self.task_manager = AsyncTaskManager(max_concurrent_tasks=10)
        
        # Thread pool for CPU-intensive tasks
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        
    def performance_decorator(self, operation_name: str):
        """Decorator to automatically track performance"""
        
        def decorator(func):
            if asyncio.iscoroutinefunction(func):
                async def async_wrapper(*args, **kwargs):
                    op_id = self.performance_monitor.start_operation(operation_name)
                    try:
                        result = await func(*args, **kwargs)
                        self.performance_monitor.end_operation(op_id, success=True)
                        return result
                    except Exception as e:
                        self.performance_monitor.end_operation(op_id, success=False, error_message=str(e))
                        raise
                return async_wrapper
            else:
                def sync_wrapper(*args, **kwargs):
                    op_id = self.performance_monitor.start_operation(operation_name)
                    try:
                        result = func(*args, **kwargs)
                        self.performance_monitor.end_operation(op_id, success=True)
                        return result
                    except Exception as e:
                        self.performance_monitor.end_operation(op_id, success=False, error_message=str(e))
                        raise
                return sync_wrapper
        return decorator
    
    async def optimize_system_performance(self) -> Dict[str, Any]:
        """Run system performance optimization"""
        
        optimization_results = {
            "cache_optimization": self._optimize_cache(),
            "memory_optimization": self._optimize_memory(),
            "task_optimization": self._optimize_tasks(),
            "performance_summary": self.performance_monitor.get_performance_summary()
        }
        
        return optimization_results
    
    def _optimize_cache(self) -> Dict[str, Any]:
        """Optimize cache performance"""
        
        # Cache statistics
        cache_size = len(self.cache_manager.cache)
        max_size = self.cache_manager.max_size
        utilization = (cache_size / max_size) * 100
        
        # Optimize TTL if cache is underutilized
        if utilization < 50:
            self.cache_manager.ttl_seconds = min(600, self.cache_manager.ttl_seconds * 1.2)
        elif utilization > 90:
            self.cache_manager.ttl_seconds = max(60, self.cache_manager.ttl_seconds * 0.8)
        
        return {
            "cache_size": cache_size,
            "max_size": max_size,
            "utilization_percent": utilization,
            "ttl_seconds": self.cache_manager.ttl_seconds,
            "optimization": "TTL adjusted based on utilization"
        }
    
    def _optimize_memory(self) -> Dict[str, Any]:
        """Optimize memory usage"""
        
        try:
            import gc
            collected = gc.collect()
            
            return {
                "garbage_collected": collected,
                "optimization": "Garbage collection performed"
            }
        except Exception as e:
            return {"error": str(e)}
    
    def _optimize_tasks(self) -> Dict[str, Any]:
        """Optimize async task management"""
        
        task_status = self.task_manager.get_task_status()
        
        # Adjust concurrency based on load
        active_tasks = task_status["active_tasks"]
        if active_tasks < 3:
            # Increase concurrency for low load
            self.task_manager.semaphore = asyncio.Semaphore(15)
        elif active_tasks > 8:
            # Decrease concurrency for high load
            self.task_manager.semaphore = asyncio.Semaphore(5)
        
        return {
            "task_status": task_status,
            "optimization": "Concurrency limits adjusted based on load"
        }
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health status"""
        
        performance_summary = self.performance_monitor.get_performance_summary(hours=1)
        cache_stats = self._optimize_cache()
        task_status = self.task_manager.get_task_status()
        
        # Calculate overall health score
        health_score = 100
        
        if performance_summary.get("error_rate", 0) > 5:
            health_score -= 20
        
        if performance_summary.get("avg_response_time_ms", 0) > 2000:
            health_score -= 15
        
        if cache_stats.get("utilization_percent", 0) > 95:
            health_score -= 10
        
        health_score = max(0, health_score)
        
        if health_score >= 90:
            health_status = "EXCELLENT"
        elif health_score >= 75:
            health_status = "GOOD"
        elif health_score >= 60:
            health_status = "FAIR"
        else:
            health_status = "CRITICAL"
        
        return {
            "health_score": health_score,
            "health_status": health_status,
            "performance_summary": performance_summary,
            "cache_stats": cache_stats,
            "task_status": task_status,
            "recommendations": self._generate_health_recommendations(health_score, performance_summary)
        }
    
    def _generate_health_recommendations(self, health_score: float, performance_summary: Dict) -> List[str]:
        """Generate health improvement recommendations"""
        
        recommendations = []
        
        if health_score < 75:
            recommendations.append("System performance needs attention")
        
        if performance_summary.get("error_rate", 0) > 5:
            recommendations.append("High error rate detected - check system logs")
        
        if performance_summary.get("avg_response_time_ms", 0) > 2000:
            recommendations.append("Slow response times - consider optimization")
        
        if not recommendations:
            recommendations.append("System is performing well - no immediate action needed")
        
        return recommendations
