"""
A.T.L.A.S Compliance and Audit System
Regulatory compliance checks, audit trails, and security controls for institutional deployment
"""

import logging
import sqlite3
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import uuid

from config import settings


class ComplianceLevel(Enum):
    """Compliance requirement levels"""
    BASIC = "basic"           # Basic retail compliance
    INSTITUTIONAL = "institutional"  # Institutional requirements
    REGULATORY = "regulatory"  # Full regulatory compliance


class AuditEventType(Enum):
    """Types of events to audit"""
    TRADE_DECISION = "trade_decision"
    RISK_OVERRIDE = "risk_override"
    USER_ACTION = "user_action"
    SYSTEM_ALERT = "system_alert"
    COMPLIANCE_CHECK = "compliance_check"
    DATA_ACCESS = "data_access"
    CONFIGURATION_CHANGE = "configuration_change"


@dataclass
class ComplianceRule:
    """Individual compliance rule"""
    rule_id: str
    name: str
    description: str
    level: ComplianceLevel
    enabled: bool
    parameters: Dict[str, Any]


@dataclass
class AuditEvent:
    """Audit trail event"""
    event_id: str
    event_type: AuditEventType
    timestamp: datetime
    user_id: str
    session_id: str
    description: str
    data: Dict[str, Any]
    risk_level: str
    compliance_status: str


@dataclass
class ComplianceCheckResult:
    """Result of compliance check"""
    passed: bool
    rule_violations: List[str]
    warnings: List[str]
    required_actions: List[str]
    risk_score: float
    audit_trail_id: str


class ComplianceEngine:
    """
    Compliance engine for regulatory and institutional requirements
    """
    
    def __init__(self, db_path: str):
        self.logger = logging.getLogger(__name__)
        self.db_path = db_path
        self._init_compliance_tables()
        
        # Load compliance rules
        self.compliance_rules = self._load_compliance_rules()
        
        # Compliance thresholds
        self.thresholds = {
            "max_daily_trades": 100,
            "max_position_size_percent": 20.0,
            "max_daily_loss_percent": 3.0,
            "max_leverage": 2.0,
            "min_account_size": 1000.0,
            "max_concentration_percent": 25.0
        }
    
    def _init_compliance_tables(self):
        """Initialize compliance and audit database tables"""
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Audit trail table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS audit_trail (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        event_id TEXT UNIQUE NOT NULL,
                        event_type TEXT NOT NULL,
                        timestamp TIMESTAMP NOT NULL,
                        user_id TEXT NOT NULL,
                        session_id TEXT NOT NULL,
                        description TEXT NOT NULL,
                        data_json TEXT NOT NULL,
                        risk_level TEXT NOT NULL,
                        compliance_status TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Compliance violations table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS compliance_violations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        violation_id TEXT UNIQUE NOT NULL,
                        rule_id TEXT NOT NULL,
                        severity TEXT NOT NULL,
                        description TEXT NOT NULL,
                        user_id TEXT NOT NULL,
                        session_id TEXT NOT NULL,
                        violation_data TEXT NOT NULL,
                        resolved BOOLEAN DEFAULT FALSE,
                        resolution_notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        resolved_at TIMESTAMP
                    )
                """)
                
                # Risk assessments table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS risk_assessments (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        assessment_id TEXT UNIQUE NOT NULL,
                        user_id TEXT NOT NULL,
                        assessment_type TEXT NOT NULL,
                        risk_score REAL NOT NULL,
                        risk_factors TEXT NOT NULL,
                        recommendations TEXT NOT NULL,
                        valid_until TIMESTAMP NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Error initializing compliance tables: {e}")
    
    def _load_compliance_rules(self) -> Dict[str, ComplianceRule]:
        """Load compliance rules configuration"""
        
        rules = {
            "daily_loss_limit": ComplianceRule(
                rule_id="daily_loss_limit",
                name="Daily Loss Limit",
                description="Maximum daily loss percentage",
                level=ComplianceLevel.BASIC,
                enabled=True,
                parameters={"max_loss_percent": 3.0}
            ),
            "position_size_limit": ComplianceRule(
                rule_id="position_size_limit", 
                name="Position Size Limit",
                description="Maximum position size as percentage of account",
                level=ComplianceLevel.BASIC,
                enabled=True,
                parameters={"max_position_percent": 20.0}
            ),
            "concentration_limit": ComplianceRule(
                rule_id="concentration_limit",
                name="Portfolio Concentration Limit", 
                description="Maximum concentration in single security",
                level=ComplianceLevel.INSTITUTIONAL,
                enabled=True,
                parameters={"max_concentration_percent": 25.0}
            ),
            "leverage_limit": ComplianceRule(
                rule_id="leverage_limit",
                name="Leverage Limit",
                description="Maximum leverage allowed",
                level=ComplianceLevel.INSTITUTIONAL,
                enabled=True,
                parameters={"max_leverage": 2.0}
            ),
            "trade_frequency_limit": ComplianceRule(
                rule_id="trade_frequency_limit",
                name="Trade Frequency Limit",
                description="Maximum trades per day",
                level=ComplianceLevel.REGULATORY,
                enabled=True,
                parameters={"max_daily_trades": 100}
            )
        }
        
        return rules
    
    async def check_compliance(self, user_id: str, session_id: str, 
                             trade_request: Dict[str, Any]) -> ComplianceCheckResult:
        """Perform comprehensive compliance check"""
        
        violations = []
        warnings = []
        required_actions = []
        risk_score = 0.0
        
        try:
            # Check each compliance rule
            for rule_id, rule in self.compliance_rules.items():
                if not rule.enabled:
                    continue
                
                violation = await self._check_rule(rule, user_id, trade_request)
                if violation:
                    violations.append(violation)
                    risk_score += 10.0  # Add to risk score
            
            # Calculate overall risk score
            risk_score = min(risk_score, 100.0)
            
            # Determine if compliance check passed
            passed = len(violations) == 0
            
            # Generate audit trail
            audit_event = AuditEvent(
                event_id=str(uuid.uuid4()),
                event_type=AuditEventType.COMPLIANCE_CHECK,
                timestamp=datetime.now(),
                user_id=user_id,
                session_id=session_id,
                description=f"Compliance check for trade request",
                data=trade_request,
                risk_level="HIGH" if risk_score > 50 else "MEDIUM" if risk_score > 20 else "LOW",
                compliance_status="PASSED" if passed else "FAILED"
            )
            
            await self._record_audit_event(audit_event)
            
            # Record violations if any
            for violation in violations:
                await self._record_compliance_violation(violation, user_id, session_id, audit_event.event_id)
            
            return ComplianceCheckResult(
                passed=passed,
                rule_violations=violations,
                warnings=warnings,
                required_actions=required_actions,
                risk_score=risk_score,
                audit_trail_id=audit_event.event_id
            )
            
        except Exception as e:
            self.logger.error(f"Error in compliance check: {e}")
            return ComplianceCheckResult(
                passed=False,
                rule_violations=[f"Compliance system error: {str(e)}"],
                warnings=[],
                required_actions=["Contact compliance team"],
                risk_score=100.0,
                audit_trail_id=""
            )
    
    async def _check_rule(self, rule: ComplianceRule, user_id: str, 
                         trade_request: Dict[str, Any]) -> Optional[str]:
        """Check individual compliance rule"""
        
        try:
            if rule.rule_id == "daily_loss_limit":
                # Check daily loss limit
                daily_loss = await self._get_daily_loss(user_id)
                max_loss = rule.parameters["max_loss_percent"]
                if daily_loss > max_loss:
                    return f"Daily loss limit exceeded: {daily_loss:.1f}% > {max_loss:.1f}%"
            
            elif rule.rule_id == "position_size_limit":
                # Check position size limit
                position_size_percent = trade_request.get("position_size_percent", 0)
                max_position = rule.parameters["max_position_percent"]
                if position_size_percent > max_position:
                    return f"Position size limit exceeded: {position_size_percent:.1f}% > {max_position:.1f}%"
            
            elif rule.rule_id == "concentration_limit":
                # Check portfolio concentration
                symbol = trade_request.get("symbol", "")
                concentration = await self._get_symbol_concentration(user_id, symbol)
                max_concentration = rule.parameters["max_concentration_percent"]
                if concentration > max_concentration:
                    return f"Concentration limit exceeded for {symbol}: {concentration:.1f}% > {max_concentration:.1f}%"
            
            elif rule.rule_id == "leverage_limit":
                # Check leverage limit
                leverage = trade_request.get("leverage", 1.0)
                max_leverage = rule.parameters["max_leverage"]
                if leverage > max_leverage:
                    return f"Leverage limit exceeded: {leverage:.1f}x > {max_leverage:.1f}x"
            
            elif rule.rule_id == "trade_frequency_limit":
                # Check trade frequency
                daily_trades = await self._get_daily_trade_count(user_id)
                max_trades = rule.parameters["max_daily_trades"]
                if daily_trades >= max_trades:
                    return f"Daily trade limit exceeded: {daily_trades} >= {max_trades}"
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error checking rule {rule.rule_id}: {e}")
            return f"Error checking {rule.name}: {str(e)}"
    
    async def _record_audit_event(self, event: AuditEvent):
        """Record audit event to database"""
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO audit_trail (
                        event_id, event_type, timestamp, user_id, session_id,
                        description, data_json, risk_level, compliance_status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    event.event_id,
                    event.event_type.value,
                    event.timestamp,
                    event.user_id,
                    event.session_id,
                    event.description,
                    json.dumps(event.data),
                    event.risk_level,
                    event.compliance_status
                ))
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Error recording audit event: {e}")
    
    async def _record_compliance_violation(self, violation: str, user_id: str, 
                                         session_id: str, audit_event_id: str):
        """Record compliance violation"""
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                violation_id = str(uuid.uuid4())
                
                cursor.execute("""
                    INSERT INTO compliance_violations (
                        violation_id, rule_id, severity, description,
                        user_id, session_id, violation_data
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    violation_id,
                    "unknown",  # Would extract from violation message
                    "HIGH",
                    violation,
                    user_id,
                    session_id,
                    json.dumps({"audit_event_id": audit_event_id})
                ))
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Error recording compliance violation: {e}")
    
    async def _get_daily_loss(self, user_id: str) -> float:
        """Get current daily loss percentage"""
        # Simplified - would calculate from actual trades
        return 0.0
    
    async def _get_symbol_concentration(self, user_id: str, symbol: str) -> float:
        """Get portfolio concentration for symbol"""
        # Simplified - would calculate from actual portfolio
        return 0.0
    
    async def _get_daily_trade_count(self, user_id: str) -> int:
        """Get number of trades today"""
        # Simplified - would count from actual trades
        return 0
    
    async def get_compliance_report(self, user_id: str, days: int = 30) -> Dict[str, Any]:
        """Generate compliance report for user"""
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cutoff_date = datetime.now() - timedelta(days=days)
                
                # Get audit events
                cursor.execute("""
                    SELECT event_type, risk_level, compliance_status, COUNT(*)
                    FROM audit_trail 
                    WHERE user_id = ? AND timestamp > ?
                    GROUP BY event_type, risk_level, compliance_status
                """, (user_id, cutoff_date))
                
                audit_summary = cursor.fetchall()
                
                # Get violations
                cursor.execute("""
                    SELECT rule_id, severity, COUNT(*), 
                           SUM(CASE WHEN resolved THEN 1 ELSE 0 END) as resolved_count
                    FROM compliance_violations 
                    WHERE user_id = ? AND created_at > ?
                    GROUP BY rule_id, severity
                """, (user_id, cutoff_date))
                
                violations_summary = cursor.fetchall()
                
                return {
                    "user_id": user_id,
                    "report_period_days": days,
                    "audit_summary": [
                        {
                            "event_type": row[0],
                            "risk_level": row[1], 
                            "compliance_status": row[2],
                            "count": row[3]
                        }
                        for row in audit_summary
                    ],
                    "violations_summary": [
                        {
                            "rule_id": row[0],
                            "severity": row[1],
                            "total_violations": row[2],
                            "resolved_violations": row[3]
                        }
                        for row in violations_summary
                    ],
                    "compliance_score": self._calculate_compliance_score(audit_summary, violations_summary),
                    "generated_at": datetime.now().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"Error generating compliance report: {e}")
            return {"error": str(e)}
    
    def _calculate_compliance_score(self, audit_summary: List, violations_summary: List) -> float:
        """Calculate overall compliance score (0-100)"""
        
        base_score = 100.0
        
        # Deduct points for violations
        for violation in violations_summary:
            severity = violation[1]
            count = violation[2]
            
            if severity == "HIGH":
                base_score -= count * 10
            elif severity == "MEDIUM":
                base_score -= count * 5
            else:
                base_score -= count * 2
        
        return max(0.0, base_score)
