"""
A.T.L.A.S Chain-of-Thought Trading Orchestrator
Master orchestrator that integrates all Chain-of-Thought components
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any

from .config import settings
from .models import (
    ChainOfThoughtAnalysis, ProfitTargetedStrategy, RiskManagementProfile,
    OptionsEducationContext, Quote, Position
)
from .chain_of_thought_engine import ChainOfThoughtEngine
from .profit_strategy_engine import ProfitTargetedStrategyEngine
from .risk_management_engine import RiskManagementEngine, PositionSizeCalculation, PreTradeValidation
from .options_education_engine import OptionsEducationEngine
from .execution_monitoring_engine import ExecutionMonitoringEngine, LiveTradeExecution, PortfolioMonitoring
from .market_data import MarketDataService
from .multi_agent_system import MultiAgentCoordinator
from .rl_execution_engine import SmartOrderRouter, ExecutionStrategy
from .compliance_audit_system import ComplianceEngine
from .performance_optimizer import PerformanceOptimizer


class ChainOfThoughtTradingOrchestrator:
    """
    Master orchestrator for Chain-of-Thought trading system
    Provides comprehensive beginner-friendly trading intelligence
    """
    
    def __init__(self, mentor_mode: bool = True):
        self.logger = logging.getLogger(__name__)

        # Initialize all engines
        self.cot_engine = ChainOfThoughtEngine()
        self.strategy_engine = ProfitTargetedStrategyEngine()
        self.risk_engine = RiskManagementEngine()
        self.options_engine = OptionsEducationEngine()
        self.execution_engine = ExecutionMonitoringEngine()
        self.market_data = MarketDataService()

        # Initialize multi-agent system for institutional-grade analysis
        self.multi_agent_coordinator = MultiAgentCoordinator()

        # Initialize RL-based smart order router for institutional execution
        self.smart_order_router = SmartOrderRouter()

        # Initialize compliance and audit system for regulatory requirements
        self.compliance_engine = ComplianceEngine("atlas_compliance.db")

        # Initialize performance optimizer for scalability and monitoring
        self.performance_optimizer = PerformanceOptimizer()

        # Mentor mode configuration for enhanced conversational intelligence
        self.mentor_mode = mentor_mode
        self.mentor_config = {
            "tone": "encouraging_but_realistic",
            "explanation_style": "beginner_friendly",
            "use_analogies": True,
            "provide_education": True,
            "reality_check_enabled": True,
            "adaptive_complexity": True
        }

        # Safety guardrails
        self.safety_guardrails = {
            "daily_loss_limit_percent": 3.0,
            "max_position_correlation": 0.85,
            "vix_trading_threshold": 40.0,
            "min_confidence_threshold": 0.70,
            "paper_trading_required": True,
            "max_positions": 6
        }

        # Mentor response templates for different scenarios
        self.mentor_responses = {
            "unrealistic_profit": "I understand you're looking for ${target}, but trying to pull that much out of {market_condition} is like {analogy}. Let me show you a safer approach that could work over {timeframe} instead...",
            "good_analysis": "Excellent thinking! You're approaching this like a professional trader. {concept} is exactly what separates successful traders from gamblers.",
            "risk_warning": "Hold on! This situation reminds me of {analogy}. Let's make sure we're being smart about risk here...",
            "educational_moment": "Great question! Think of {concept} like {analogy}. Here's how it works in simple terms:",
            "market_reality": "The market is like {analogy} right now. Here's what that means for your trading strategy..."
        }

    def _apply_mentor_style_to_plan(self, plan_result: Dict[str, Any], user_request: str, account_size: float) -> Dict[str, Any]:
        """Apply mentor-style communication to trading plan results"""

        if not self.mentor_mode:
            return plan_result

        # Extract profit target from user request
        import re
        profit_matches = re.findall(r'\$?(\d+)', user_request.lower())

        if profit_matches:
            target = float(profit_matches[0])
            daily_return_percent = (target / account_size) * 100

            # Always provide mentor guidance, regardless of plan success
            if daily_return_percent > 5:  # More than 5% return
                timeframe = "today" if any(word in user_request.lower() for word in ["today", "daily"]) else "this week"
                market_analogy = "trying to squeeze juice from a rock" if timeframe == "today" else "expecting to hit a home run every time you're at bat"

                mentor_guidance = f"""
**🎯 I understand you're looking for ${target:.0f} {timeframe}!**

**💡 Reality Check:** Making ${target:.0f} {timeframe} would be a {daily_return_percent:.1f}% return. That's like {market_analogy} - possible, but not sustainable.

**📚 Professional Perspective:**
What you're asking for is what hedge funds dream of achieving monthly. Warren Buffett averages about 20% per YEAR.

**🎓 Here's what I can do instead:**
Let me show you a realistic approach that could work over a longer timeframe and actually build sustainable wealth.
                """.strip()

                plan_result["mentor_guidance"] = mentor_guidance

            elif daily_return_percent > 2:  # 2-5% return - ambitious but possible
                mentor_guidance = f"""
**🎯 Great question about making ${target:.0f}!**

**💡 Think of it this way:** You're asking for a {daily_return_percent:.1f}% return, which is ambitious but not impossible with the right approach.

**🎓 Smart Approach:**
Instead of forcing quick gains, let me show you how to identify high-probability setups that could realistically achieve this over a few days with proper risk management.
                """.strip()

                plan_result["mentor_guidance"] = mentor_guidance

            else:  # Reasonable expectations
                mentor_guidance = f"""
**🎯 Excellent! ${target:.0f} is a realistic goal!**

**💡 You're thinking like a professional trader:** A {daily_return_percent:.1f}% return is achievable with proper strategy and risk management.

**🎓 Let's build a plan:**
I'll show you exactly how to identify the right opportunities and manage risk to reach this goal safely.
                """.strip()

                plan_result["mentor_guidance"] = mentor_guidance

        # Add encouraging educational context
        if self.mentor_config["provide_education"]:
            plan_result["mentor_encouragement"] = "You're asking exactly the right questions! This is how successful traders approach the market."

        return plan_result

    def _generate_mentor_educational_summary(self, analysis: ChainOfThoughtAnalysis,
                                           position_calc: PositionSizeCalculation,
                                           execution_result: Dict[str, Any]) -> str:
        """Generate mentor-style educational summary"""

        if not self.mentor_mode:
            return self._generate_execution_educational_summary(analysis, position_calc, execution_result)

        summary = f"""
**🎓 What We Just Did (Mentor Explanation):**

**Step 1: Market Analysis**
Think of this like checking the weather before a road trip. We analyzed {analysis.symbol} using our Chain-of-Thought process to understand what the market is telling us.

**Step 2: Risk Management**
Like wearing a seatbelt, we calculated exactly how much to risk (${position_calc.risk_amount:.0f}) so that even if this trade goes wrong, your account stays healthy.

**Step 3: Position Sizing**
We used mathematical formulas (Kelly Criterion) to determine the optimal position size - not too big to hurt you, not too small to miss opportunity.

**Step 4: Safety Checks**
Our system has multiple safety nets, like having airbags, anti-lock brakes, AND a seatbelt. Every trade gets these protections automatically.

**🧠 Why This Approach Works:**
Professional traders don't gamble - they calculate. Every decision we made was based on data, not emotions. This is how you build wealth consistently over time.

**📚 Key Learning:**
The difference between trading and gambling is having a systematic approach with proper risk management. You're learning to trade like an institution!
        """.strip()

        return summary

    async def create_comprehensive_trading_plan(self,
                                              user_request: str,
                                              account_size: float,
                                              risk_tolerance: str = "moderate") -> Dict[str, Any]:
        """
        Create comprehensive trading plan with Chain-of-Thought analysis
        """
        
        try:
            # Parse user request for profit target
            profit_target = self._extract_profit_target(user_request, account_size)
            
            # Create profit-targeted strategy
            strategy = await self.strategy_engine.create_profit_targeted_strategy(
                profit_target=profit_target,
                account_size=account_size,
                timeframe="intraday",
                risk_tolerance=risk_tolerance
            )
            
            if not strategy:
                return {
                    "success": False,
                    "message": "Unable to create strategy with current parameters",
                    "educational_note": "Try adjusting your profit target or risk tolerance"
                }
            
            # Execute strategy scan with Chain-of-Thought analysis
            trade_opportunities = await self.strategy_engine.execute_strategy_scan(strategy)
            
            # Analyze each opportunity with full Chain-of-Thought
            detailed_analyses = []
            for opportunity in trade_opportunities[:3]:  # Top 3 opportunities
                
                # Get comprehensive Chain-of-Thought analysis
                cot_analysis = await self.cot_engine.analyze_ttm_squeeze_with_cot(
                    opportunity["symbol"]
                )
                
                if cot_analysis:
                    # Calculate position sizing
                    position_calc = self.risk_engine.calculate_position_size(
                        symbol=opportunity["symbol"],
                        entry_price=opportunity["entry_price"],
                        account_size=account_size,
                        confidence=cot_analysis.final_confidence,
                        risk_profile=RiskManagementProfile(
                            account_size=account_size,
                            daily_loss_limit_percent=self.safety_guardrails["daily_loss_limit_percent"]
                        )
                    )
                    
                    # Pre-trade validation
                    validation = self.risk_engine.validate_trade_setup(
                        symbol=opportunity["symbol"],
                        entry_price=opportunity["entry_price"],
                        position_size=position_calc,
                        current_positions=[],  # Would get actual positions
                        cot_analysis=cot_analysis
                    )
                    
                    detailed_analyses.append({
                        "symbol": opportunity["symbol"],
                        "chain_of_thought": cot_analysis,
                        "position_sizing": position_calc,
                        "validation": validation,
                        "opportunity_data": opportunity
                    })
            
            # Generate comprehensive plan
            comprehensive_plan = self._generate_comprehensive_plan(
                strategy, detailed_analyses, account_size
            )

            plan_result = {
                "success": True,
                "strategy": strategy,
                "trade_opportunities": detailed_analyses,
                "comprehensive_plan": comprehensive_plan,
                "safety_notes": self._generate_safety_notes(),
                "educational_summary": self._generate_educational_summary(strategy, detailed_analyses)
            }

            # Apply mentor-style enhancements
            if self.mentor_mode:
                plan_result = self._apply_mentor_style_to_plan(plan_result, user_request, account_size)

            return plan_result

        except Exception as e:
            self.logger.error(f"Error creating comprehensive trading plan: {e}")

            # Even on error, provide mentor-style guidance
            mentor_guidance = self._create_error_mentor_response(user_request, account_size, str(e))

            return {
                "success": False,
                "message": f"Error creating plan: {str(e)}",
                "mentor_guidance": mentor_guidance,
                "educational_note": "Let me help you understand what went wrong and how to move forward safely."
            }

    def _create_error_mentor_response(self, user_request: str, account_size: float, error: str) -> str:
        """Create mentor-style response even when plan creation fails"""

        # Extract profit target if possible
        import re
        profit_matches = re.findall(r'\$?(\d+)', user_request.lower())

        if profit_matches:
            target = float(profit_matches[0])
            safe_target = min(target, account_size * 0.02)  # Max 2% of account

            return f"""
**🎯 I understand you want to make ${target:.0f}!**

**🔧 Technical Issue:** We encountered a system error, but that doesn't stop me from helping you.

**💡 Here's what I can do right now:**
1. **Start with a safer target:** ${safe_target:.0f} (2% of your account)
2. **Learn the fundamentals:** Ask me about TTM Squeeze setups
3. **Practice first:** Try paper trading to build confidence
4. **Build gradually:** Master small wins before bigger goals

**🎓 Remember:** The best traders focus on consistent small gains rather than home runs. Let's start there!

**Next steps:** Ask me "Teach me about risk management" or "Show me a simple trading setup"
            """.strip()
        else:
            return """
**🔧 We hit a technical snag, but I'm still here to help!**

**💡 Let's start with the basics:**
• Ask me about risk management
• Learn about TTM Squeeze signals
• Try paper trading first
• Build your knowledge step by step

**🎓 Remember:** Every expert was once a beginner. Let's build your foundation properly!
            """.strip()

    async def execute_multi_agent_analysis(self, symbol: str, account_size: float) -> Dict[str, Any]:
        """
        Execute comprehensive multi-agent analysis for institutional-grade decision making
        """

        try:
            # Get enhanced market context
            async with self.market_data:
                enhanced_context = await self.market_data.get_enhanced_market_context(symbol)

                if "error" in enhanced_context:
                    return {
                        "success": False,
                        "message": enhanced_context["error"],
                        "educational_note": "Unable to gather sufficient market data for analysis"
                    }

                # Get historical data for technical analysis
                historical_data = await self.market_data.get_historical_data(symbol, "5min", 50)

                # Prepare market data for agents
                market_data_for_agents = {
                    **enhanced_context,
                    "historical_data": historical_data,
                    "account_size": account_size
                }

                # Execute multi-agent analysis
                agent_consensus = await self.multi_agent_coordinator.analyze_symbol(symbol, market_data_for_agents)

                # Generate educational explanation of multi-agent decision
                educational_explanation = self._generate_multi_agent_educational_explanation(
                    symbol, agent_consensus, enhanced_context
                )

                # Calculate position sizing based on consensus
                position_sizing = self._calculate_consensus_position_sizing(
                    agent_consensus, account_size, enhanced_context
                )

                return {
                    "success": True,
                    "symbol": symbol,
                    "agent_consensus": {
                        "final_decision": agent_consensus.final_decision.name,
                        "consensus_confidence": agent_consensus.consensus_confidence,
                        "disagreement_score": agent_consensus.disagreement_score,
                        "risk_assessment": agent_consensus.risk_assessment,
                        "execution_recommendation": agent_consensus.execution_recommendation
                    },
                    "agent_analyses": {
                        agent_type.value: {
                            "decision": analysis.decision.name,
                            "confidence": analysis.confidence,
                            "reasoning": analysis.reasoning,
                            "risk_factors": analysis.risk_factors
                        }
                        for agent_type, analysis in agent_consensus.agent_votes.items()
                    },
                    "position_sizing": position_sizing,
                    "educational_explanation": educational_explanation,
                    "enhanced_context": enhanced_context
                }

        except Exception as e:
            self.logger.error(f"Error in multi-agent analysis for {symbol}: {e}")
            return {
                "success": False,
                "message": f"Multi-agent analysis error: {str(e)}",
                "educational_note": "The institutional-grade analysis system encountered an error. Please try again."
            }

    def _generate_multi_agent_educational_explanation(self, symbol: str, consensus, enhanced_context: dict) -> str:
        """Generate educational explanation of multi-agent decision process"""

        explanation = f"""
🤖 **Institutional-Grade Multi-Agent Analysis for {symbol}**

**🎯 Final Consensus**: {consensus.final_decision.name}
**📊 Confidence Level**: {consensus.consensus_confidence*100:.1f}%
**⚖️ Agent Agreement**: {(1-consensus.disagreement_score)*100:.1f}%
**🛡️ Risk Assessment**: {consensus.risk_assessment}

**🔍 How Our AI Specialists Analyzed This Trade:**

**Technical Analysis Agent (35% weight):**
{consensus.agent_votes.get('technical', type('obj', (object,), {'reasoning': 'Not available', 'decision': type('obj', (object,), {'name': 'NEUTRAL'})()})).reasoning}
Decision: {consensus.agent_votes.get('technical', type('obj', (object,), {'decision': type('obj', (object,), {'name': 'NEUTRAL'})()})).decision.name}

**Risk Management Agent (30% weight):**
{consensus.agent_votes.get('risk', type('obj', (object,), {'reasoning': 'Not available', 'decision': type('obj', (object,), {'name': 'NEUTRAL'})()})).reasoning}
Decision: {consensus.agent_votes.get('risk', type('obj', (object,), {'decision': type('obj', (object,), {'name': 'NEUTRAL'})()})).decision.name}

**Sentiment Analysis Agent (20% weight):**
{consensus.agent_votes.get('sentiment', type('obj', (object,), {'reasoning': 'Not available', 'decision': type('obj', (object,), {'name': 'NEUTRAL'})()})).reasoning}
Decision: {consensus.agent_votes.get('sentiment', type('obj', (object,), {'decision': type('obj', (object,), {'name': 'NEUTRAL'})()})).decision.name}

**Execution Timing Agent (15% weight):**
{consensus.agent_votes.get('execution', type('obj', (object,), {'reasoning': 'Not available', 'decision': type('obj', (object,), {'name': 'NEUTRAL'})()})).reasoning}
Decision: {consensus.agent_votes.get('execution', type('obj', (object,), {'decision': type('obj', (object,), {'name': 'NEUTRAL'})()})).decision.name}

**🎓 Educational Insight:**
This multi-agent approach mirrors how institutional trading desks operate - different specialists analyze the same opportunity from their expertise areas, then the head trader makes the final decision based on all inputs. This reduces single-point-of-failure risks and provides more robust analysis.

**⚡ Execution Recommendation:**
{consensus.execution_recommendation}

**🧠 AI Enhancement:**
{enhanced_context.get('predicto_ai_recommendation', 'Predicto AI analysis not available')}
        """.strip()

        return explanation

    def _calculate_consensus_position_sizing(self, consensus, account_size: float, enhanced_context: dict) -> dict:
        """Calculate position sizing based on multi-agent consensus"""

        # Base position size from consensus confidence
        base_size_pct = consensus.consensus_confidence * 3.0  # Max 3% at 100% confidence

        # Risk adjustment based on disagreement
        disagreement_penalty = consensus.disagreement_score * 0.5  # Reduce size for disagreement

        # Risk assessment adjustment
        risk_multiplier = {
            "LOW": 1.0,
            "MODERATE": 0.8,
            "HIGH": 0.5
        }.get(consensus.risk_assessment, 0.5)

        # Final position size
        final_size_pct = max(0.5, (base_size_pct - disagreement_penalty) * risk_multiplier)
        final_size_pct = min(final_size_pct, 2.5)  # Cap at 2.5%

        # Calculate dollar amounts
        position_value = account_size * (final_size_pct / 100)

        # Get current price for share calculation
        current_price = enhanced_context.get("current_price", 100)  # Fallback price
        shares = int(position_value / current_price) if current_price > 0 else 0

        return {
            "position_size_pct": final_size_pct,
            "position_value": position_value,
            "recommended_shares": shares,
            "risk_amount": position_value * 0.02,  # 2% risk per trade
            "consensus_confidence": consensus.consensus_confidence,
            "risk_assessment": consensus.risk_assessment
        }

    async def execute_rl_optimized_trade(self, symbol: str, quantity: int, side: str,
                                       strategy: str = "adaptive") -> Dict[str, Any]:
        """Execute trade using RL-optimized smart order routing"""

        try:
            # Map strategy string to enum
            execution_strategy = {
                "aggressive": ExecutionStrategy.AGGRESSIVE,
                "passive": ExecutionStrategy.PASSIVE,
                "adaptive": ExecutionStrategy.ADAPTIVE,
                "stealth": ExecutionStrategy.STEALTH
            }.get(strategy, ExecutionStrategy.ADAPTIVE)

            # Execute using smart order router
            execution_result = await self.smart_order_router.execute_order(
                symbol=symbol,
                quantity=quantity,
                side=side,
                strategy=execution_strategy,
                max_time_minutes=30
            )

            # Add educational explanation
            if execution_result["success"]:
                educational_explanation = f"""
**🤖 RL-Optimized Execution Complete**

**📊 Execution Summary:**
• Symbol: {symbol}
• Quantity: {quantity:,} shares
• Fill Rate: {execution_result['fill_rate']*100:.1f}%
• Execution Time: {execution_result['execution_time_seconds']:.1f} seconds
• Average Price: ${execution_result['avg_price']:.2f}

**🧠 AI Learning Insights:**
• Total RL Executions: {execution_result['rl_performance']['total_executions']}
• Average Slippage: {execution_result['rl_performance']['avg_slippage']*100:.3f}%
• Success Rate: {execution_result['rl_performance']['success_rate']*100:.1f}%

**🎓 Educational Note:**
Our RL system learns from each execution to optimize future trades. This institutional-grade
approach minimizes market impact while maximizing fill rates - the same technology used by
professional trading firms.
                """.strip()

                execution_result["educational_explanation"] = educational_explanation

            return execution_result

        except Exception as e:
            self.logger.error(f"Error in RL-optimized execution: {e}")
            return {
                "success": False,
                "error": str(e),
                "educational_note": "RL execution system encountered an error. Falling back to standard execution methods."
            }

    def get_rl_execution_analytics(self) -> Dict[str, Any]:
        """Get RL execution system performance analytics"""

        try:
            analytics = self.smart_order_router.get_execution_analytics()

            # Add educational context
            analytics["educational_summary"] = f"""
**🤖 RL Execution System Performance**

**📈 Learning Progress:**
• Total Executions: {analytics['total_executions']}
• Q-Table Size: {analytics['q_table_size']} states learned
• Exploration Rate: {analytics['learning_progress']['exploration_rate']*100:.1f}%

**🎯 Performance Metrics:**
• Average Slippage: {analytics['rl_performance']['avg_slippage']*100:.3f}%
• Average Market Impact: {analytics['rl_performance']['avg_market_impact']*100:.3f}%
• Success Rate: {analytics['rl_performance']['success_rate']*100:.1f}%

**🎓 What This Means:**
The RL system continuously learns optimal execution strategies from market conditions.
Lower slippage and market impact mean better trade execution and higher profits for you.
            """.strip()

            return analytics

        except Exception as e:
            self.logger.error(f"Error getting RL analytics: {e}")
            return {"error": str(e)}

    async def check_trade_compliance(self, user_id: str, session_id: str,
                                   trade_request: Dict[str, Any]) -> Dict[str, Any]:
        """Check trade compliance and generate audit trail"""

        try:
            # Perform compliance check
            compliance_result = await self.compliance_engine.check_compliance(
                user_id=user_id,
                session_id=session_id,
                trade_request=trade_request
            )

            # Generate educational explanation
            if compliance_result.passed:
                educational_explanation = f"""
**✅ Compliance Check Passed**

**🛡️ Safety Verification Complete:**
• All regulatory requirements met
• Risk score: {compliance_result.risk_score:.1f}/100
• Audit trail ID: {compliance_result.audit_trail_id[:8]}...

**🎓 What This Means:**
Your trade request meets all safety and regulatory requirements. Our institutional-grade
compliance system ensures every trade follows best practices for risk management.
                """.strip()
            else:
                educational_explanation = f"""
**⚠️ Compliance Issues Detected**

**🚫 Rule Violations:**
{chr(10).join(f"• {violation}" for violation in compliance_result.rule_violations)}

**🛡️ Required Actions:**
{chr(10).join(f"• {action}" for action in compliance_result.required_actions)}

**🎓 Educational Note:**
These compliance rules protect your capital and ensure responsible trading. Think of them
like safety equipment - they might seem restrictive, but they prevent serious harm.

**💡 Next Steps:**
• Reduce position size to meet limits
• Wait for daily loss limit to reset
• Consider paper trading to practice
                """.strip()

            return {
                "compliance_passed": compliance_result.passed,
                "risk_score": compliance_result.risk_score,
                "violations": compliance_result.rule_violations,
                "warnings": compliance_result.warnings,
                "required_actions": compliance_result.required_actions,
                "audit_trail_id": compliance_result.audit_trail_id,
                "educational_explanation": educational_explanation
            }

        except Exception as e:
            self.logger.error(f"Error in compliance check: {e}")
            return {
                "compliance_passed": False,
                "error": str(e),
                "educational_explanation": "Compliance system error - please contact support"
            }

    async def get_compliance_report(self, user_id: str, days: int = 30) -> Dict[str, Any]:
        """Get comprehensive compliance report for user"""

        try:
            report = await self.compliance_engine.get_compliance_report(user_id, days)

            # Add educational summary
            if "error" not in report:
                compliance_score = report.get("compliance_score", 0)

                if compliance_score >= 90:
                    grade = "EXCELLENT"
                    message = "Outstanding compliance record! You're trading like a professional."
                elif compliance_score >= 75:
                    grade = "GOOD"
                    message = "Good compliance record with room for improvement."
                elif compliance_score >= 60:
                    grade = "FAIR"
                    message = "Fair compliance record - focus on following risk management rules."
                else:
                    grade = "NEEDS IMPROVEMENT"
                    message = "Compliance needs attention - please review risk management practices."

                report["educational_summary"] = f"""
**📊 Compliance Report Summary**

**🎯 Compliance Score: {compliance_score:.1f}/100 ({grade})**

{message}

**📈 Report Period:** {days} days
**🔍 Total Audit Events:** {len(report.get('audit_summary', []))}
**⚠️ Total Violations:** {sum(v.get('total_violations', 0) for v in report.get('violations_summary', []))}

**🎓 Educational Insight:**
Compliance tracking helps you become a better trader by identifying patterns and areas
for improvement. High compliance scores indicate disciplined, professional trading habits.
                """.strip()

            return report

        except Exception as e:
            self.logger.error(f"Error getting compliance report: {e}")
            return {"error": str(e)}

    async def get_system_performance(self) -> Dict[str, Any]:
        """Get comprehensive system performance metrics"""

        try:
            # Get performance optimization results
            optimization_results = await self.performance_optimizer.optimize_system_performance()

            # Get system health
            health_status = self.performance_optimizer.get_system_health()

            # Add educational summary
            health_score = health_status.get("health_score", 0)
            performance_grade = health_status.get("health_status", "UNKNOWN")

            educational_summary = f"""
**🚀 A.T.L.A.S System Performance Report**

**🎯 Overall Health: {health_score:.1f}/100 ({performance_grade})**

**📊 Key Metrics:**
• Response Time: {health_status.get('performance_summary', {}).get('avg_response_time_ms', 0):.1f}ms
• Success Rate: {health_status.get('performance_summary', {}).get('success_rate', 0):.1f}%
• Memory Usage: {health_status.get('cache_stats', {}).get('utilization_percent', 0):.1f}%
• Active Tasks: {health_status.get('task_status', {}).get('active_tasks', 0)}

**🎓 What This Means:**
{performance_grade} performance indicates our AI trading system is {'running optimally' if health_score >= 90 else 'performing well' if health_score >= 75 else 'needs attention'}.
These metrics help ensure you get fast, reliable trading intelligence.

**💡 Recommendations:**
{chr(10).join(f"• {rec}" for rec in health_status.get('recommendations', []))}
            """.strip()

            return {
                "health_status": health_status,
                "optimization_results": optimization_results,
                "educational_summary": educational_summary,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error getting system performance: {e}")
            return {
                "error": str(e),
                "educational_note": "Performance monitoring system encountered an error"
            }

    def apply_performance_decorators(self):
        """Apply performance monitoring to key methods"""

        # Decorate key methods with performance monitoring
        original_analyze = self.execute_multi_agent_analysis
        self.execute_multi_agent_analysis = self.performance_optimizer.performance_decorator("multi_agent_analysis")(original_analyze)

        original_plan = self.create_comprehensive_trading_plan
        self.create_comprehensive_trading_plan = self.performance_optimizer.performance_decorator("trading_plan_creation")(original_plan)

        original_cot = self.analyze_symbol_with_cot
        self.analyze_symbol_with_cot = self.performance_optimizer.performance_decorator("cot_analysis")(original_cot)
    
    async def execute_trade_with_full_cot(self, 
                                        symbol: str,
                                        account_size: float,
                                        confidence_override: Optional[float] = None) -> Dict[str, Any]:
        """
        Execute trade with full Chain-of-Thought analysis and protection
        """
        
        try:
            # Step 1: Chain-of-Thought Analysis
            cot_analysis = await self.cot_engine.analyze_ttm_squeeze_with_cot(symbol)
            
            if not cot_analysis:
                return {
                    "success": False,
                    "message": f"Unable to analyze {symbol} - insufficient data",
                    "educational_note": "Try a different symbol or check market data availability"
                }
            
            # Step 2: Position Sizing with Risk Management
            quote = await self.market_data.get_real_time_quote(symbol)
            
            position_calc = self.risk_engine.calculate_position_size(
                symbol=symbol,
                entry_price=quote.price,
                account_size=account_size,
                confidence=confidence_override or cot_analysis.final_confidence,
                risk_profile=RiskManagementProfile(account_size=account_size)
            )
            
            # Step 3: Pre-trade Validation
            validation = self.risk_engine.validate_trade_setup(
                symbol=symbol,
                entry_price=quote.price,
                position_size=position_calc,
                current_positions=[],  # Would get actual positions
                cot_analysis=cot_analysis
            )
            
            # Step 4: Execute if validated
            if validation.is_valid:
                execution_result = await self.execution_engine.execute_trade_with_protection(
                    symbol=symbol,
                    position_calc=position_calc,
                    validation=validation
                )
                
                # Choose educational summary based on mentor mode
                if self.mentor_mode:
                    educational_summary = self._generate_mentor_educational_summary(
                        cot_analysis, position_calc, execution_result
                    )
                else:
                    educational_summary = self._generate_execution_educational_summary(
                        cot_analysis, position_calc, execution_result
                    )

                return {
                    "success": True,
                    "chain_of_thought": cot_analysis,
                    "position_sizing": position_calc,
                    "validation": validation,
                    "execution": execution_result,
                    "educational_summary": educational_summary
                }
            else:
                return {
                    "success": False,
                    "chain_of_thought": cot_analysis,
                    "position_sizing": position_calc,
                    "validation": validation,
                    "message": "Trade blocked by risk management",
                    "educational_note": "Safety first! The system prevented a potentially risky trade."
                }
                
        except Exception as e:
            self.logger.error(f"Error executing trade with CoT for {symbol}: {e}")
            return {
                "success": False,
                "message": f"Execution error: {str(e)}",
                "educational_note": "Technical error occurred - please try again"
            }
    
    async def get_portfolio_dashboard(self, account_size: float) -> Dict[str, Any]:
        """
        Get comprehensive portfolio dashboard with educational insights
        """
        
        try:
            # Real-time portfolio monitoring
            portfolio_monitoring = await self.execution_engine.monitor_portfolio_realtime(account_size)
            
            # Market condition assessment
            market_conditions = {
                "condition": portfolio_monitoring.market_condition.value,
                "trading_allowed": portfolio_monitoring.market_condition.value in ["normal", "volatile"],
                "vix_level": 25.0,  # Would get real VIX
                "market_hours": self._check_market_hours()
            }
            
            # Educational insights based on performance
            educational_insights = self._generate_portfolio_educational_insights(portfolio_monitoring)
            
            # Risk utilization analysis
            risk_analysis = {
                "daily_risk_used": portfolio_monitoring.daily_risk_used,
                "risk_limit_remaining": portfolio_monitoring.risk_limit_remaining,
                "risk_utilization_percent": (portfolio_monitoring.daily_risk_used / (account_size * 0.03)) * 100,
                "position_count": portfolio_monitoring.open_positions,
                "diversification_score": self._calculate_diversification_score()
            }
            
            return {
                "portfolio_monitoring": portfolio_monitoring,
                "market_conditions": market_conditions,
                "risk_analysis": risk_analysis,
                "educational_insights": educational_insights,
                "safety_status": self._check_safety_status(portfolio_monitoring),
                "next_actions": self._suggest_next_actions(portfolio_monitoring, market_conditions)
            }
            
        except Exception as e:
            self.logger.error(f"Error generating portfolio dashboard: {e}")
            return {
                "error": str(e),
                "educational_note": "Dashboard temporarily unavailable - please refresh"
            }
    
    def _extract_profit_target(self, user_request: str, account_size: float) -> float:
        """Extract profit target from user request"""
        
        # Simple extraction logic (would be more sophisticated)
        import re
        
        # Look for dollar amounts
        dollar_matches = re.findall(r'\$(\d+(?:,\d{3})*(?:\.\d{2})?)', user_request)
        if dollar_matches:
            return float(dollar_matches[0].replace(',', ''))
        
        # Look for percentage
        percent_matches = re.findall(r'(\d+(?:\.\d+)?)%', user_request)
        if percent_matches:
            return account_size * (float(percent_matches[0]) / 100)
        
        # Default to 1% of account
        return account_size * 0.01
    
    def _generate_comprehensive_plan(self, strategy: ProfitTargetedStrategy,
                                   analyses: List[Dict], account_size: float) -> str:
        """Generate comprehensive trading plan explanation"""
        
        plan = f"""
🎯 A.T.L.A.S COMPREHENSIVE TRADING PLAN

💰 PROFIT TARGET: ${strategy.profit_target:.2f} ({(strategy.profit_target/account_size)*100:.1f}% of account)
⏰ TIMEFRAME: {strategy.timeframe.title()}
🎲 EXPECTED WIN RATE: {strategy.expected_win_rate*100:.0f}%

📊 STRATEGY OVERVIEW:
{strategy.strategy_reasoning}

🔍 TOP OPPORTUNITIES ANALYZED:
"""
        
        for i, analysis in enumerate(analyses[:3], 1):
            cot = analysis["chain_of_thought"]
            plan += f"""
{i}. {analysis["symbol"]} - Confidence: {cot.final_confidence*100:.0f}%
   💡 {cot.final_recommendation}
   💰 Position Size: {analysis["position_sizing"].recommended_shares} shares (${analysis["position_sizing"].dollar_amount:,.2f})
   🛡️ Risk: ${analysis["position_sizing"].risk_amount:.2f} | Target: ${analysis["position_sizing"].target_price:.2f}
"""
        
        plan += f"""
🛡️ RISK MANAGEMENT:
• Maximum daily risk: ${account_size * 0.03:.2f} (3% of account)
• Position sizing: Kelly Criterion with safety factor
• Automatic stop-losses on every trade
• Real-time monitoring and alerts

📚 EDUCATIONAL NOTES:
• This plan uses proven TTM Squeeze patterns with {strategy.expected_win_rate*100:.0f}% historical success rate
• Each trade is sized mathematically for optimal risk-adjusted returns
• Multiple safety layers protect your capital
• System provides step-by-step reasoning for every decision

⚠️ IMPORTANT: No strategy is guaranteed. Always trade with money you can afford to lose.
        """.strip()
        
        return plan
    
    def _generate_safety_notes(self) -> List[str]:
        """Generate safety notes for users"""
        
        return [
            "🛡️ All trades include automatic stop-losses to limit downside risk",
            "📊 Position sizes are calculated mathematically using Kelly Criterion",
            "⏰ Trading is automatically suspended during high volatility periods",
            "💰 Daily loss limits prevent catastrophic losses",
            "📚 Every decision includes educational explanations for learning",
            "🎯 Paper trading is recommended for new users to practice safely"
        ]
    
    def _generate_educational_summary(self, strategy: ProfitTargetedStrategy,
                                    analyses: List[Dict]) -> str:
        """Generate educational summary"""
        
        return f"""
📚 EDUCATIONAL SUMMARY

🧠 WHAT IS CHAIN-OF-THOUGHT TRADING?
Chain-of-Thought trading means the AI explains every step of its reasoning, just like a human trader would think through a decision. Instead of just saying "buy this stock," it explains WHY, HOW MUCH, and WHAT COULD GO WRONG.

🔍 THE TTM SQUEEZE PATTERN:
This strategy looks for stocks that are "coiled like a spring" - when Bollinger Bands squeeze inside Keltner Channels, it often leads to explosive moves. Think of it like a rubber band being stretched tight.

💰 POSITION SIZING SCIENCE:
We use the Kelly Criterion, a mathematical formula that tells us the optimal bet size to grow your account while minimizing risk of ruin. It's like having a mathematical advisor for every trade.

🎯 WHY THIS APPROACH WORKS:
• Combines proven technical patterns with mathematical position sizing
• Provides transparency in every decision
• Includes multiple safety layers
• Focuses on education, not just profits

Remember: The goal isn't just to make money, but to learn and improve as a trader!
        """.strip()
    
    def _generate_execution_educational_summary(self, cot_analysis: ChainOfThoughtAnalysis,
                                              position_calc: PositionSizeCalculation,
                                              execution: LiveTradeExecution) -> str:
        """Generate educational summary for execution"""
        
        return f"""
🎓 WHAT JUST HAPPENED - EDUCATIONAL BREAKDOWN

🔍 ANALYSIS PROCESS:
The AI analyzed {cot_analysis.symbol} through {len(cot_analysis.steps)} logical steps:
{chr(10).join([f"• {step.title}: {step.explanation[:100]}..." for step in cot_analysis.steps[:3]])}

💰 POSITION SIZING LOGIC:
{position_calc.educational_explanation}

🎯 EXECUTION DETAILS:
{execution.educational_explanation}

💡 KEY LEARNING POINTS:
• Chain-of-thought analysis provides transparency in decision-making
• Mathematical position sizing optimizes risk-adjusted returns
• Automatic protective orders manage downside risk
• Real-time monitoring ensures disciplined execution

This is how professional traders think - systematically, mathematically, and with proper risk management!
        """.strip()
    
    def _generate_portfolio_educational_insights(self, monitoring: PortfolioMonitoring) -> List[str]:
        """Generate educational insights based on portfolio performance"""
        
        insights = []
        
        if monitoring.daily_pnl > 0:
            insights.append("📈 Profitable day! Remember that consistency matters more than big wins")
            insights.append("💡 Consider taking some profits if you're up significantly")
        else:
            insights.append("📉 Red days are normal in trading - your risk management is protecting you")
            insights.append("🎯 Focus on process, not just results - good decisions lead to good outcomes")
        
        if monitoring.open_positions > 4:
            insights.append("📊 Multiple positions provide diversification but require more monitoring")
        
        if abs(monitoring.daily_pnl_percent) > 2:
            insights.append("⚡ High volatility day - consider reducing position sizes")
        
        return insights
    
    def _check_market_hours(self) -> Dict[str, Any]:
        """Check market hours and trading windows"""
        
        now = datetime.now().time()
        
        return {
            "market_open": now >= datetime.strptime("09:30", "%H:%M").time(),
            "safe_trading_window": datetime.strptime("10:00", "%H:%M").time() <= now <= datetime.strptime("15:30", "%H:%M").time(),
            "market_close_soon": now >= datetime.strptime("15:30", "%H:%M").time()
        }
    
    def _calculate_diversification_score(self) -> float:
        """Calculate portfolio diversification score"""
        # Simplified calculation - would be more sophisticated
        return 75.0  # Placeholder
    
    def _check_safety_status(self, monitoring: PortfolioMonitoring) -> Dict[str, Any]:
        """Check overall safety status"""
        
        safety_score = 100.0
        issues = []
        
        if abs(monitoring.daily_pnl_percent) > 2:
            safety_score -= 20
            issues.append("High daily volatility")
        
        if monitoring.open_positions > 5:
            safety_score -= 10
            issues.append("High position count")
        
        return {
            "safety_score": max(safety_score, 0),
            "status": "SAFE" if safety_score >= 80 else "CAUTION" if safety_score >= 60 else "WARNING",
            "issues": issues
        }
    
    def _suggest_next_actions(self, monitoring: PortfolioMonitoring, 
                            market_conditions: Dict[str, Any]) -> List[str]:
        """Suggest next actions based on current state"""
        
        actions = []
        
        if not market_conditions["trading_allowed"]:
            actions.append("⏸️ Wait for better market conditions before new trades")
        elif monitoring.daily_pnl_percent < -2:
            actions.append("🛑 Consider stopping trading for today - preserve capital")
        elif monitoring.open_positions == 0:
            actions.append("🔍 Scan for new opportunities using Chain-of-Thought analysis")
        else:
            actions.append("👀 Monitor existing positions and wait for clear signals")
        
        return actions
