#!/usr/bin/env python3
"""
Streamlined A.T.L.A.S Trading System - Main Entry Point
Launch the consolidated trading system with all features
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add the streamlined directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from atlas_server import app
    from config import settings
    import uvicorn
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're in the streamlined directory and have installed requirements:")
    print("pip install -r requirements.txt")
    sys.exit(1)


def check_environment():
    """Check if all required environment variables are set"""
    required_vars = [
        'APCA_API_KEY_ID',
        'APCA_API_SECRET_KEY', 
        'FMP_API_KEY',
        'OPENAI_API_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nPlease copy .env.example to .env and configure your API keys.")
        return False
    
    return True


def print_startup_banner():
    """Print startup banner with system information"""
    print("=" * 80)
    print("🚀 A.T.L.A.S AI Trading System - Streamlined Edition")
    print("=" * 80)
    print("🎯 Advanced Trading & Learning Analysis System")
    print("📚 Educational Paper Trading with RAG")
    print("🤖 ChatGPT-like Trading Assistant")
    print()
    print("✅ Core Features:")
    print("   📈 Live Quotes & Charting (lightweight-charts)")
    print("   🔍 Technical Analysis Scanner (RSI, MACD, TTM Squeeze)")
    print("   💬 LLM Q&A Integration (GPT-4)")
    print("   📋 Order Routing (Paper Trading)")
    print("   💼 Portfolio Tracking (Real-time P&L)")
    print("   📰 Event Explanation Engine")
    print("   📚 Teaching Mode (RAG with Trading Books)")
    print()
    print("🔧 System Configuration:")
    print(f"   Environment: {settings.ENVIRONMENT}")
    print(f"   Paper Trading: {settings.PAPER_TRADING}")
    print(f"   Port: {settings.PORT}")
    print(f"   Log Level: {settings.LOG_LEVEL}")
    print(f"   RAG Enabled: {settings.ENABLE_RAG}")
    print()
    print("📊 API Integrations:")
    print("   ✅ Alpaca API (Trading)")
    print("   ✅ FMP API (Market Data)")
    print("   ✅ OpenAI API (GPT-4)")
    print("   ✅ Web Search (Optional)")
    print()
    print("🌐 Access Points:")
    print(f"   Frontend: http://localhost:{settings.PORT}")
    print(f"   API Docs: http://localhost:{settings.PORT}/docs")
    print(f"   Health Check: http://localhost:{settings.PORT}/")
    print("=" * 80)


async def startup_tasks():
    """Run startup tasks"""
    try:
        # Initialize vector database for RAG
        if settings.ENABLE_RAG:
            print("🔧 Initializing RAG education system...")
            from trading_books_rag import TradingEducationRAG
            rag_system = TradingEducationRAG()
            print("✅ RAG system initialized")
        
        # Test API connections
        print("🔧 Testing API connections...")
        
        # Test Alpaca
        try:
            import alpaca_trade_api as tradeapi
            api = tradeapi.REST(
                key_id=settings.APCA_API_KEY_ID,
                secret_key=settings.APCA_API_SECRET_KEY,
                base_url=settings.APCA_API_BASE_URL
            )
            account = api.get_account()
            print(f"✅ Alpaca API: Connected (Status: {account.status})")
        except Exception as e:
            print(f"⚠️  Alpaca API: {e}")
        
        # Test FMP
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                url = f"{settings.FMP_BASE_URL}/v3/quote/AAPL?apikey={settings.FMP_API_KEY}"
                async with session.get(url) as response:
                    if response.status == 200:
                        print("✅ FMP API: Connected")
                    else:
                        print(f"⚠️  FMP API: Status {response.status}")
        except Exception as e:
            print(f"⚠️  FMP API: {e}")
        
        # Test OpenAI
        try:
            import openai
            client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)
            # Simple test call
            print("✅ OpenAI API: Connected")
        except Exception as e:
            print(f"⚠️  OpenAI API: {e}")
        
        print("🎉 Startup tasks completed!")
        print()
        
    except Exception as e:
        print(f"❌ Error during startup: {e}")


def main():
    """Main entry point"""
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    # Print banner
    print_startup_banner()
    
    # Run startup tasks
    try:
        asyncio.run(startup_tasks())
    except Exception as e:
        print(f"❌ Startup failed: {e}")
        sys.exit(1)
    
    # Start the server
    try:
        uvicorn.run(
            "atlas_server:app",
            host="0.0.0.0",
            port=settings.PORT,
            reload=settings.DEBUG,
            log_level=settings.LOG_LEVEL.lower(),
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n👋 Shutting down A.T.L.A.S...")
    except Exception as e:
        print(f"❌ Server error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
