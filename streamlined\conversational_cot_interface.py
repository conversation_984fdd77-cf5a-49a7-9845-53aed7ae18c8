"""
A.T.L.A.S Conversational Chain-of-Thought Interface
Integrates Chain-of-Thought trading intelligence with ChatGPT-style conversation
"""

import asyncio
import logging
import re
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple

from .config import settings
from .models import ChatMessage, AIResponse
from .cot_trading_orchestrator import ChainOfThoughtTradingOrchestrator
from .safety_guardrails import SafetyGuardrailsEngine, SafetyAssessment
from .ai_services import AIServices


class ConversationalCoTInterface:
    """
    Conversational interface that integrates Chain-of-Thought trading intelligence
    with natural language processing for beginner-friendly interactions
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.cot_orchestrator = ChainOfThoughtTradingOrchestrator(mentor_mode=True)
        self.safety_engine = SafetyGuardrailsEngine()
        self.ai_services = AIServices()

        # Conversation context with enhanced user profiling
        self.conversation_history: List[ChatMessage] = []
        self.user_profile = {
            "account_size": 25000.0,  # Default
            "risk_tolerance": "moderate",
            "experience_level": "beginner",  # auto-detected
            "paper_trading": True,
            "communication_style": "mentor",  # mentor, professional, casual
            "explanation_depth": "detailed",  # brief, detailed, comprehensive
            "analogy_preference": True,
            "learning_mode": True
        }

        # Enhanced user experience detection patterns
        self.experience_indicators = {
            "beginner": [
                "what is", "how do", "explain", "teach me", "i'm new", "beginner",
                "don't understand", "confused", "simple terms", "basic", "help me learn",
                "first time", "never traded", "getting started"
            ],
            "intermediate": [
                "support", "resistance", "moving average", "rsi", "macd", "bollinger",
                "volume", "breakout", "trend", "chart pattern", "technical analysis",
                "fibonacci", "candlestick", "momentum", "oversold", "overbought"
            ],
            "advanced": [
                "gamma", "theta", "vega", "delta", "greeks", "volatility skew",
                "iron condor", "butterfly", "straddle", "strangle", "kelly criterion",
                "sharpe ratio", "sortino", "maximum drawdown", "var", "cvar",
                "black-scholes", "implied volatility", "options chain", "put-call ratio"
            ]
        }
        
        # Intent patterns for natural language understanding
        self.intent_patterns = {
            "create_plan": [
                r"make me \$?(\d+)",
                r"profit target.*\$?(\d+)",
                r"trading plan.*\$?(\d+)",
                r"earn.*\$?(\d+)",
                r"goal.*\$?(\d+)"
            ],
            "analyze_symbol": [
                r"analyze ([A-Z]{1,5})",
                r"what.*think.*([A-Z]{1,5})",
                r"should.*buy ([A-Z]{1,5})",
                r"([A-Z]{1,5}).*good.*trade",
                r"chain.*thought.*([A-Z]{1,5})"
            ],
            "portfolio_status": [
                r"portfolio",
                r"dashboard",
                r"how.*doing",
                r"performance",
                r"positions"
            ],
            "education": [
                r"what.*is.*ttm",
                r"explain.*squeeze",
                r"how.*kelly",
                r"what.*means",
                r"teach.*me"
            ],
            "risk_management": [
                r"risk",
                r"stop.*loss",
                r"position.*size",
                r"safety",
                r"dangerous"
            ]
        }

    def _detect_and_update_user_experience(self, message: str) -> str:
        """Detect user experience level and update profile"""

        message_lower = message.lower()

        # Count indicators for each level
        scores = {}
        for level, indicators in self.experience_indicators.items():
            scores[level] = sum(1 for indicator in indicators if indicator in message_lower)

        # Determine experience level
        if scores["advanced"] > 0:
            detected_level = "advanced"
        elif scores["intermediate"] > scores["beginner"]:
            detected_level = "intermediate"
        else:
            detected_level = "beginner"

        # Update user profile if confidence is high
        if scores[detected_level] >= 2 or detected_level == "advanced":
            self.user_profile["experience_level"] = detected_level

        return detected_level

    def _adapt_communication_style(self, response: str, user_level: str, intent: str) -> str:
        """Adapt communication style based on user experience level"""

        # Add appropriate level of detail
        if user_level == "beginner" and self.user_profile["explanation_depth"] == "detailed":
            response = self._add_beginner_context(response, intent)
        elif user_level == "advanced":
            response = self._add_advanced_insights(response, intent)

        # Add analogies for beginners
        if user_level == "beginner" and self.user_profile["analogy_preference"]:
            response = self._add_trading_analogies(response, intent)

        return response

    def _add_beginner_context(self, response: str, intent: str) -> str:
        """Add beginner-friendly context and explanations"""

        beginner_contexts = {
            "create_plan": "\n\n**🎓 For New Traders:** A trading plan is like a GPS for your money - it tells you where to go, how to get there, and what to do if you get lost.",
            "analyze_symbol": "\n\n**🎓 Learning Note:** When we analyze a stock, we're like detectives looking for clues about where the price might go next.",
            "portfolio_status": "\n\n**🎓 Portfolio Basics:** Think of your portfolio like a garden - you need to tend to it regularly and make sure everything is growing healthily.",
            "risk_management": "\n\n**🎓 Risk 101:** Risk management is like wearing protective gear - it might feel unnecessary until you need it, then you're grateful you have it."
        }

        context = beginner_contexts.get(intent, "")
        return response + context

    def _add_advanced_insights(self, response: str, intent: str) -> str:
        """Add advanced insights for experienced traders"""

        advanced_insights = {
            "create_plan": "\n\n**📊 Advanced Note:** Consider correlation analysis and sector rotation patterns for enhanced diversification.",
            "analyze_symbol": "\n\n**📊 Technical Insight:** Review options flow and institutional positioning for additional confirmation signals.",
            "portfolio_status": "\n\n**📊 Performance Analytics:** Monitor risk-adjusted returns and consider factor exposure analysis.",
            "risk_management": "\n\n**📊 Risk Metrics:** Evaluate VaR, CVaR, and tail risk measures for comprehensive risk assessment."
        }

        insight = advanced_insights.get(intent, "")
        return response + insight

    def _add_trading_analogies(self, response: str, intent: str) -> str:
        """Add relatable trading analogies for better understanding"""

        analogies = {
            "create_plan": "Creating a trading plan is like planning a road trip - you need to know your destination, route, and what to do if you encounter roadblocks.",
            "analyze_symbol": "Analyzing a stock is like being a detective - you gather clues from charts, news, and market behavior to solve the mystery of where the price is heading.",
            "portfolio_status": "Your portfolio is like a sports team - each position plays a role, and you need good teamwork (diversification) to win consistently.",
            "risk_management": "Risk management is like having insurance - you hope you never need it, but you're grateful when you do."
        }

        if intent in analogies and "analogy" not in response.lower():
            analogy = analogies[intent]
            response += f"\n\n**💡 Think of it this way:** {analogy}"

        return response

    async def _provide_mentor_analysis(self, original_message: str, profit_target: float, account_size: float) -> str:
        """Provide mentor-style analysis of user request with reality checks and analogies"""

        # Calculate daily return percentage
        daily_return_percent = (profit_target / account_size) * 100

        # Detect timeframe from message
        message_lower = original_message.lower()
        timeframe = "today" if any(word in message_lower for word in ["today", "daily"]) else "this week"

        # Reality check for unrealistic expectations
        if daily_return_percent > 5:  # More than 5% return
            if timeframe == "today":
                analogy = "trying to squeeze juice from a rock"
                professional_comparison = "hedge funds dream of achieving monthly"
            else:
                analogy = "expecting to hit a home run every time you're at bat"
                professional_comparison = "professional traders aim for monthly"

            mentor_response = f"""
**🎯 I understand you're looking for ${profit_target:.0f} {timeframe}!**

**💡 Reality Check:** Making ${profit_target:.0f} {timeframe} would be a {daily_return_percent:.1f}% return. That's like {analogy} - possible, but not sustainable.

**📚 Professional Perspective:**
What you're asking for is what {professional_comparison}. Warren Buffett averages about 20% per YEAR.

**🎓 Here's what I can do instead:**
Let me show you a realistic approach that could work over a longer timeframe and actually build sustainable wealth.
            """.strip()

        elif daily_return_percent > 2:  # 2-5% return
            mentor_response = f"""
**🎯 Great question about making ${profit_target:.0f} {timeframe}!**

**💡 Think of it this way:** You're asking for a {daily_return_percent:.1f}% return, which is ambitious but not impossible with the right approach.

**🎓 Smart Approach:**
Instead of forcing quick gains, let me show you how to identify high-probability setups that could realistically achieve this over a few days with proper risk management.
            """.strip()

        else:  # Reasonable expectations
            mentor_response = f"""
**🎯 Excellent! ${profit_target:.0f} {timeframe} is a realistic goal!**

**💡 You're thinking like a professional trader:** A {daily_return_percent:.1f}% return is achievable with proper strategy and risk management.

**🎓 Let's build a plan:**
I'll show you exactly how to identify the right opportunities and manage risk to reach this goal safely.
            """.strip()

        return mentor_response

    async def _create_educational_alternative(self, original_message: str, profit_target: float,
                                            account_size: float, safety_assessment) -> str:
        """Create educational alternative when safety issues prevent the original plan"""

        # Calculate a safer alternative target
        safe_target = min(profit_target, account_size * 0.02)  # Max 2% of account
        safe_return_percent = (safe_target / account_size) * 100

        alternative_response = f"""
**🛡️ Safety-First Alternative Plan**

While I can't create a plan for ${profit_target:.0f} right now due to safety concerns, here's what I CAN help you with:

**🎯 Safer Target:** ${safe_target:.0f} ({safe_return_percent:.1f}% of your account)
**⏰ Realistic Timeframe:** 3-5 trading days
**🎓 Educational Approach:** Learn while you earn

**📋 Your Step-by-Step Plan:**
1. **Start with paper trading** to practice the strategy
2. **Learn TTM Squeeze setups** - our highest probability signals
3. **Master risk management** - protect your capital first
4. **Scale up gradually** as you gain experience

**🧠 Why This Works:**
Think of this like learning to drive - you start in a parking lot, not on the highway. Once you master the basics, we can work toward bigger goals safely.

**💡 Next Steps:**
• Ask me: "Teach me about TTM Squeeze"
• Try: "Show me a paper trading setup"
• Learn: "How do I calculate position size?"

Remember: The best traders prioritize learning over quick profits! 🎓
        """.strip()

        return alternative_response

    async def process_user_message(self, message: str, user_context: Optional[Dict] = None) -> AIResponse:
        """
        Process user message with Chain-of-Thought intelligence
        """
        
        try:
            # Update user profile if provided
            if user_context:
                self.user_profile.update(user_context)

            # Detect and update user experience level
            detected_level = self._detect_and_update_user_experience(message)

            # Add user message to conversation history
            user_msg = ChatMessage(role="user", content=message)
            self.conversation_history.append(user_msg)

            # Detect intent and extract parameters
            intent, parameters = self._detect_intent(message)

            # Route to appropriate handler
            if intent == "create_plan":
                response = await self._handle_create_plan(parameters, message)
            elif intent == "analyze_symbol":
                response = await self._handle_analyze_symbol(parameters, message)
            elif intent == "portfolio_status":
                response = await self._handle_portfolio_status(message)
            elif intent == "education":
                response = await self._handle_education_request(message)
            elif intent == "risk_management":
                response = await self._handle_risk_management(message)
            else:
                response = await self._handle_general_conversation(message)

            # Apply adaptive communication style based on user experience
            if response and response.response:
                response.response = self._adapt_communication_style(
                    response.response, detected_level, intent
                )

            # Add assistant response to conversation history
            assistant_msg = ChatMessage(
                role="assistant",
                content=response.response,
                function_result={
                    "intent": intent,
                    "parameters": parameters,
                    "user_experience_level": detected_level
                }
            )
            self.conversation_history.append(assistant_msg)

            return response
            
        except Exception as e:
            self.logger.error(f"Error processing user message: {e}")
            return AIResponse(
                response="I'm having trouble processing your request right now. Could you please try rephrasing it?",
                type="error",
                confidence=0.0
            )
    
    def _detect_intent(self, message: str) -> Tuple[str, Dict[str, Any]]:
        """Detect user intent and extract parameters"""
        
        message_lower = message.lower()
        
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, message_lower)
                if match:
                    parameters = {}
                    
                    if intent == "create_plan" and match.group(1):
                        parameters["profit_target"] = float(match.group(1))
                    elif intent == "analyze_symbol" and match.group(1):
                        parameters["symbol"] = match.group(1).upper()
                    
                    return intent, parameters
        
        return "general", {}
    
    async def _handle_create_plan(self, parameters: Dict, original_message: str) -> AIResponse:
        """Handle trading plan creation with mentor-style response first"""

        profit_target = parameters.get("profit_target", 100.0)  # Default $100
        account_size = self.user_profile["account_size"]

        # STEP 1: Provide mentor-style analysis FIRST (before safety blocks)
        mentor_response = await self._provide_mentor_analysis(original_message, profit_target, account_size)

        # STEP 2: Check if we can create a safety-compliant plan
        safety_assessment = self.safety_engine.comprehensive_safety_check(
            symbol="SPY",  # Generic check
            position_size_dollars=profit_target * 10,  # Estimate position size
            account_size=account_size,
            confidence=0.75,  # Assume moderate confidence
            current_positions=[]  # Would get real positions
        )

        # STEP 3: If safety issues exist, provide educational alternative instead of blocking
        if not safety_assessment.trading_allowed:
            educational_alternative = await self._create_educational_alternative(
                original_message, profit_target, account_size, safety_assessment
            )

            return AIResponse(
                response=mentor_response + "\n\n" + educational_alternative,
                type="mentor_guidance",
                confidence=0.9
            )
        
        # Create comprehensive trading plan
        plan_result = await self.cot_orchestrator.create_comprehensive_trading_plan(
            user_request=original_message,
            account_size=self.user_profile["account_size"],
            risk_tolerance=self.user_profile["risk_tolerance"]
        )
        
        if plan_result["success"]:
            strategy = plan_result["strategy"]
            opportunities = plan_result["trade_opportunities"]
            
            response_text = f"""
🎯 **Your Personalized Trading Plan**

**Goal:** ${strategy.profit_target:.0f} ({(strategy.profit_target/self.user_profile['account_size'])*100:.1f}% of your account)

**🧠 Chain-of-Thought Analysis Found {len(opportunities)} Opportunities:**

"""
            
            for i, opp in enumerate(opportunities[:3], 1):
                cot = opp["chain_of_thought"]
                response_text += f"""
**{i}. {opp["symbol"]}** - Confidence: {cot.final_confidence*100:.0f}%
💡 *Why this trade?* {cot.final_recommendation[:100]}...
💰 *Position:* {opp["position_sizing"].recommended_shares} shares (${opp["position_sizing"].dollar_amount:,.0f})
🛡️ *Risk:* ${opp["position_sizing"].risk_amount:.0f} | 🎯 *Target:* ${opp["position_sizing"].target_price:.2f}

"""
            
            response_text += f"""
**📚 Educational Summary:**
{plan_result["educational_summary"][:300]}...

**🛡️ Safety Features Active:**
• Automatic stop-losses on every trade
• Daily loss limit: ${self.user_profile['account_size'] * 0.03:.0f} (3% of account)
• Position sizing optimized mathematically
• Real-time risk monitoring

**Ready to proceed?** Ask me to "analyze [SYMBOL]" for detailed Chain-of-Thought reasoning on any of these opportunities!
            """.strip()
            
            return AIResponse(
                response=response_text,
                type="trading_plan",
                trading_plan=plan_result,
                confidence=0.85
            )
        else:
            return AIResponse(
                response=f"""
😔 **Unable to Create Trading Plan**

{plan_result.get('message', 'Unknown error occurred')}

**For beginners:** This usually means market conditions aren't favorable right now, or your profit target might need adjustment.

**Try this instead:**
• "Teach me about TTM Squeeze patterns"
• "What's my portfolio status?"
• "Analyze AAPL" (or another stock you're interested in)
• Adjust your profit target to be more realistic

Remember: Patience is a trader's best friend! 🕰️
                """.strip(),
                type="plan_failed",
                confidence=0.3
            )
    
    async def _handle_analyze_symbol(self, parameters: Dict, original_message: str) -> AIResponse:
        """Handle symbol analysis requests"""
        
        symbol = parameters.get("symbol", "AAPL")
        
        # Get full Chain-of-Thought analysis
        analysis_result = await self.cot_orchestrator.execute_trade_with_full_cot(
            symbol=symbol,
            account_size=self.user_profile["account_size"]
        )
        
        if analysis_result["success"]:
            cot = analysis_result["chain_of_thought"]
            position_sizing = analysis_result["position_sizing"]
            validation = analysis_result["validation"]
            
            # Create beginner-friendly response
            response_text = f"""
🧠 **Chain-of-Thought Analysis for {symbol}**

**Final Recommendation:** {cot.final_recommendation}
**Confidence Level:** {cot.final_confidence*100:.0f}% 

**🔍 How I Analyzed This (Step-by-Step):**

"""
            
            for i, step in enumerate(cot.steps[:4], 1):  # Show first 4 steps
                response_text += f"""
**Step {i}: {step.title}**
{step.explanation[:150]}...
*Analogy:* {step.analogy[:100] if step.analogy else "N/A"}...

"""
            
            response_text += f"""
**💰 Position Sizing Recommendation:**
• **Shares to buy:** {position_sizing.recommended_shares}
• **Total investment:** ${position_sizing.dollar_amount:,.0f}
• **Risk amount:** ${position_sizing.risk_amount:.0f}
• **Stop-loss:** ${position_sizing.stop_loss_price:.2f}
• **Target price:** ${position_sizing.target_price:.2f}

**🛡️ Safety Check:** {validation.recommendation}

**📚 What This Means for Beginners:**
{analysis_result["educational_summary"][:200]}...

**Want to proceed?** Say "execute trade for {symbol}" or ask me to explain any part in more detail!
            """.strip()
            
            return AIResponse(
                response=response_text,
                type="symbol_analysis",
                confidence=cot.final_confidence
            )
        else:
            return AIResponse(
                response=f"""
😔 **Unable to Analyze {symbol}**

{analysis_result.get('message', 'Analysis failed')}

**For beginners:** This usually means:
• Not enough market data available
• Symbol might be misspelled
• Market is closed or data is delayed

**Try this instead:**
• Check the symbol spelling (e.g., "AAPL" not "Apple")
• Try a popular stock like AAPL, MSFT, or GOOGL
• Ask "what stocks should I analyze?"

I'm here to help you learn! 📚
                """.strip(),
                type="analysis_failed",
                confidence=0.2
            )
    
    async def _handle_portfolio_status(self, message: str) -> AIResponse:
        """Handle portfolio status requests"""
        
        dashboard = await self.cot_orchestrator.get_portfolio_dashboard(
            self.user_profile["account_size"]
        )
        
        if "error" not in dashboard:
            portfolio = dashboard["portfolio_monitoring"]
            safety = dashboard["safety_status"]
            insights = dashboard["educational_insights"]
            
            response_text = f"""
📊 **Your Portfolio Dashboard**

**💰 Account Summary:**
• Total Value: ${portfolio.total_value:,.2f}
• Today's P&L: ${portfolio.daily_pnl:+,.2f} ({portfolio.daily_pnl_percent:+.2f}%)
• Open Positions: {portfolio.open_positions}
• Safety Score: {safety["safety_score"]:.0f}/100 ({safety["status"]})

**🎓 Educational Insights:**
{chr(10).join([f"• {insight}" for insight in insights[:3]])}

**🛡️ Risk Management Status:**
• Daily Risk Used: ${dashboard["risk_analysis"]["daily_risk_used"]:.0f}
• Risk Remaining: ${dashboard["risk_analysis"]["risk_limit_remaining"]:.0f}
• Diversification Score: {dashboard["risk_analysis"]["diversification_score"]:.0f}%

**📈 Performance Summary:**
{portfolio.performance_summary[:200]}...

**🎯 Suggested Next Actions:**
{chr(10).join([f"• {action}" for action in dashboard["next_actions"]])}

Want to dive deeper? Ask me about any specific aspect!
            """.strip()
            
            return AIResponse(
                response=response_text,
                type="portfolio_status",
                confidence=0.9
            )
        else:
            return AIResponse(
                response="""
😔 **Portfolio Dashboard Temporarily Unavailable**

I'm having trouble accessing your portfolio data right now. This might be due to:
• Market data connectivity issues
• System maintenance
• Account connection problems

**What you can do:**
• Try again in a few minutes
• Ask me about general trading concepts
• Request a symbol analysis instead

I'll be back to full functionality soon! 🔄
                """.strip(),
                type="dashboard_error",
                confidence=0.3
            )
    
    async def _handle_education_request(self, message: str) -> AIResponse:
        """Handle educational requests"""
        
        # Use existing education RAG system
        educational_response = await self.ai_services.generate_educational_explanation(message)
        
        response_text = f"""
📚 **Educational Explanation**

{educational_response}

**🧠 Chain-of-Thought Connection:**
This concept is integrated into our trading analysis. When you ask me to analyze a stock, I use these principles in my step-by-step reasoning.

**Want to see it in action?**
• "Analyze AAPL" - See how these concepts apply to real trades
• "Create a trading plan" - See how education guides strategy
• "What's my portfolio status?" - See how theory meets practice

Learning by doing is the best way to master trading! 🎯
        """.strip()
        
        return AIResponse(
            response=response_text,
            type="education",
            confidence=0.8
        )
    
    async def _handle_risk_management(self, message: str) -> AIResponse:
        """Handle risk management questions"""
        
        response_text = """
🛡️ **Risk Management in A.T.L.A.S**

**Our Safety-First Approach:**
• **Daily Loss Limits:** Maximum 3% of your account per day
• **Position Sizing:** Mathematical Kelly Criterion optimization
• **Stop-Losses:** Automatic on every single trade
• **Diversification:** Maximum 85% correlation between positions
• **Market Conditions:** Trading paused during high volatility (VIX > 40)

**🎓 Beginner-Friendly Explanation:**
Think of risk management like wearing a seatbelt. It might feel restrictive, but it protects you from catastrophic harm. Our system has multiple "seatbelts":

1. **Position Size Limits** - Like speed limits, they prevent you from going too fast
2. **Stop-Losses** - Like airbags, they activate automatically when things go wrong
3. **Daily Limits** - Like a fuel gauge, they prevent you from running out of gas
4. **Correlation Limits** - Like diversifying your route, they prevent all roads from being blocked

**🧠 Chain-of-Thought Integration:**
Every analysis includes a dedicated risk assessment step that explains:
• What could go wrong
• How likely it is
• How to protect yourself
• Why the risk is worth taking (or not)

**Want to see this in action?** Ask me to analyze any stock and I'll show you the complete risk assessment!
        """.strip()
        
        return AIResponse(
            response=response_text,
            type="risk_education",
            confidence=0.95
        )
    
    async def _handle_general_conversation(self, message: str) -> AIResponse:
        """Handle general conversation and unclear requests"""
        
        # Use AI services for general conversation
        general_response = await self.ai_services.process_general_query(message)
        
        response_text = f"""
{general_response}

**🧠 A.T.L.A.S Chain-of-Thought Features:**
I can help you with advanced trading intelligence! Try asking:

• **"Make me $200 today"** - Get a complete trading plan
• **"Analyze AAPL"** - See step-by-step reasoning for any stock
• **"What's my portfolio status?"** - Get comprehensive dashboard
• **"Teach me about TTM Squeeze"** - Learn trading concepts
• **"How does risk management work?"** - Understand safety features

**🎓 Beginner-Friendly Promise:**
I explain every decision in simple terms with analogies. No confusing jargon, just clear reasoning you can understand and learn from!

What would you like to explore? 🚀
        """.strip()
        
        return AIResponse(
            response=response_text,
            type="general",
            confidence=0.6
        )
