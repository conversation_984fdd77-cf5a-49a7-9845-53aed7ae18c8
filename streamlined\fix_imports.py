#!/usr/bin/env python3
"""
Quick script to fix all relative imports in the streamlined directory
"""

import os
import re

def fix_imports_in_file(filepath):
    """Fix relative imports in a single file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace relative imports with absolute imports
        # Pattern: from .module_name import ...
        content = re.sub(r'from \.([a-zA-Z_][a-zA-Z0-9_]*)', r'from \1', content)
        
        # Pattern: from .submodule.module_name import ...
        content = re.sub(r'from \.([a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*)', r'from \1', content)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Fixed imports in: {filepath}")
        return True
    except Exception as e:
        print(f"Error fixing {filepath}: {e}")
        return False

def main():
    """Fix imports in all Python files in the current directory"""
    current_dir = os.getcwd()
    python_files = [f for f in os.listdir(current_dir) if f.endswith('.py') and f != 'fix_imports.py']
    
    fixed_count = 0
    for filename in python_files:
        filepath = os.path.join(current_dir, filename)
        if fix_imports_in_file(filepath):
            fixed_count += 1
    
    print(f"\nFixed imports in {fixed_count} files out of {len(python_files)} Python files.")

if __name__ == "__main__":
    main()
