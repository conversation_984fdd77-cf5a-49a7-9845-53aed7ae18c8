"""
A.T.L.A.S Intelligent Trade Recycling System
Sophisticated re-entry and profit recycling logic for maximum capital efficiency
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from .config import settings
from .models import TechnicalIndicators, Quote
from .market_data import MarketDataService
from .technical_analysis import TechnicalScanner
from .multi_agent_system import MultiAgentCoordinator


class RecyclingStrategy(Enum):
    """Trade recycling strategies"""
    PULLBACK_REENTRY = "pullback_reentry"
    PROFIT_SCALING = "profit_scaling"
    MOMENTUM_PYRAMIDING = "momentum_pyramiding"
    VOLATILITY_RECYCLING = "volatility_recycling"


@dataclass
class TradeRecyclingOpportunity:
    """Represents a trade recycling opportunity"""
    symbol: str
    strategy: RecyclingStrategy
    original_trade_id: str
    recycling_confidence: float
    expected_additional_return: float
    risk_adjustment: float
    entry_price: float
    position_size: int
    reasoning: str
    technical_reset_score: float


@dataclass
class ProfitRecyclingPlan:
    """Plan for recycling profits into new opportunities"""
    available_profit: float
    target_allocation: Dict[str, float]
    high_conviction_opportunities: List[TradeRecyclingOpportunity]
    risk_budget_remaining: float
    diversification_score: float


class TradeRecyclingEngine:
    """
    Intelligent trade recycling system that implements sophisticated re-entry
    and profit recycling logic for maximum capital efficiency
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.market_data = MarketDataService()
        self.scanner = TechnicalScanner()
        self.multi_agent = MultiAgentCoordinator()
        
        # Recycling parameters
        self.pullback_threshold = 0.02  # 2% pullback for re-entry
        self.profit_recycling_threshold = 0.03  # 3% profit to trigger recycling
        self.max_pyramid_levels = 3  # Maximum pyramiding levels
        self.technical_reset_threshold = 0.7  # Technical conditions reset score
        
        # Risk management for recycling
        self.max_recycling_risk = 0.05  # 5% of account for recycling
        self.correlation_limit = 0.7  # Maximum correlation between recycled positions
        
        # Performance tracking
        self.recycling_performance = {
            RecyclingStrategy.PULLBACK_REENTRY: {"success_rate": 0.68, "avg_return": 0.045},
            RecyclingStrategy.PROFIT_SCALING: {"success_rate": 0.72, "avg_return": 0.055},
            RecyclingStrategy.MOMENTUM_PYRAMIDING: {"success_rate": 0.65, "avg_return": 0.08},
            RecyclingStrategy.VOLATILITY_RECYCLING: {"success_rate": 0.70, "avg_return": 0.06}
        }
    
    async def scan_recycling_opportunities(self, active_positions: List[Dict], 
                                         closed_positions: List[Dict],
                                         account_metrics: Dict) -> List[TradeRecyclingOpportunity]:
        """Scan for intelligent trade recycling opportunities"""
        try:
            self.logger.info("🔄 Scanning for trade recycling opportunities...")
            
            opportunities = []
            
            # Scan for pullback re-entry opportunities
            pullback_ops = await self._scan_pullback_reentries(closed_positions)
            opportunities.extend(pullback_ops)
            
            # Scan for profit scaling opportunities
            scaling_ops = await self._scan_profit_scaling(active_positions)
            opportunities.extend(scaling_ops)
            
            # Scan for momentum pyramiding opportunities
            pyramid_ops = await self._scan_momentum_pyramiding(active_positions)
            opportunities.extend(pyramid_ops)
            
            # Scan for volatility recycling opportunities
            volatility_ops = await self._scan_volatility_recycling(closed_positions)
            opportunities.extend(volatility_ops)
            
            # Filter and rank opportunities
            filtered_ops = self._filter_recycling_opportunities(opportunities, account_metrics)
            ranked_ops = self._rank_recycling_opportunities(filtered_ops)
            
            self.logger.info(f"📊 Found {len(ranked_ops)} trade recycling opportunities")
            return ranked_ops
            
        except Exception as e:
            self.logger.error(f"Error scanning recycling opportunities: {e}")
            return []
    
    async def _scan_pullback_reentries(self, closed_positions: List[Dict]) -> List[TradeRecyclingOpportunity]:
        """Scan for pullback re-entry opportunities from recently closed profitable trades"""
        try:
            opportunities = []
            
            # Look at profitable trades closed in last 5 days
            recent_profitable = [
                pos for pos in closed_positions 
                if pos.get("profit", 0) > 0 and 
                (datetime.now() - pos.get("close_date", datetime.now())).days <= 5
            ]
            
            for position in recent_profitable:
                symbol = position["symbol"]
                original_exit_price = position["exit_price"]
                
                # Get current market data
                current_quote = await self.market_data.get_real_time_quote(symbol)
                historical_data = await self.market_data.get_historical_data(symbol, "1Day", 20)
                
                # Check for pullback conditions
                pullback_pct = (original_exit_price - current_quote.price) / original_exit_price
                
                if pullback_pct >= self.pullback_threshold:
                    # Analyze if technical conditions have reset
                    technical_reset_score = await self._analyze_technical_reset(symbol, historical_data)
                    
                    if technical_reset_score >= self.technical_reset_threshold:
                        # Get multi-agent confirmation
                        consensus = await self.multi_agent.analyze_symbol(symbol, historical_data)
                        
                        if consensus.final_decision.name in ["BUY", "STRONG_BUY"]:
                            opportunity = TradeRecyclingOpportunity(
                                symbol=symbol,
                                strategy=RecyclingStrategy.PULLBACK_REENTRY,
                                original_trade_id=position["trade_id"],
                                recycling_confidence=consensus.consensus_confidence,
                                expected_additional_return=0.045,  # 4.5% expected return
                                risk_adjustment=0.8,  # Reduced risk due to familiarity
                                entry_price=current_quote.price,
                                position_size=int(position["original_size"] * 0.75),  # 75% of original size
                                reasoning=f"Pullback re-entry: {pullback_pct:.1%} pullback with technical reset",
                                technical_reset_score=technical_reset_score
                            )
                            opportunities.append(opportunity)
            
            return opportunities
            
        except Exception as e:
            self.logger.error(f"Error scanning pullback re-entries: {e}")
            return []
    
    async def _scan_profit_scaling(self, active_positions: List[Dict]) -> List[TradeRecyclingOpportunity]:
        """Scan for profit scaling opportunities in winning positions"""
        try:
            opportunities = []
            
            for position in active_positions:
                if position.get("unrealized_profit", 0) > self.profit_recycling_threshold:
                    symbol = position["symbol"]
                    
                    # Get current market analysis
                    historical_data = await self.market_data.get_historical_data(symbol, "1Day", 20)
                    consensus = await self.multi_agent.analyze_symbol(symbol, historical_data)
                    
                    # Check if momentum is still strong
                    if (consensus.final_decision.name in ["BUY", "STRONG_BUY"] and 
                        consensus.consensus_confidence > 0.7):
                        
                        current_quote = await self.market_data.get_real_time_quote(symbol)
                        
                        opportunity = TradeRecyclingOpportunity(
                            symbol=symbol,
                            strategy=RecyclingStrategy.PROFIT_SCALING,
                            original_trade_id=position["trade_id"],
                            recycling_confidence=consensus.consensus_confidence,
                            expected_additional_return=0.055,  # 5.5% expected return
                            risk_adjustment=1.0,  # Normal risk
                            entry_price=current_quote.price,
                            position_size=int(position["size"] * 0.5),  # 50% additional size
                            reasoning=f"Profit scaling: {position['unrealized_profit']:.1%} unrealized profit",
                            technical_reset_score=0.8  # High score for continuing momentum
                        )
                        opportunities.append(opportunity)
            
            return opportunities
            
        except Exception as e:
            self.logger.error(f"Error scanning profit scaling: {e}")
            return []
    
    async def _scan_momentum_pyramiding(self, active_positions: List[Dict]) -> List[TradeRecyclingOpportunity]:
        """Scan for momentum pyramiding opportunities"""
        try:
            opportunities = []
            
            for position in active_positions:
                if (position.get("pyramid_level", 0) < self.max_pyramid_levels and
                    position.get("unrealized_profit", 0) > 0.02):  # 2% profit minimum
                    
                    symbol = position["symbol"]
                    
                    # Check for strong momentum continuation
                    historical_data = await self.market_data.get_historical_data(symbol, "1Hour", 50)
                    momentum_score = self._calculate_momentum_score(historical_data)
                    
                    if momentum_score > 0.75:  # Strong momentum
                        consensus = await self.multi_agent.analyze_symbol(symbol, historical_data)
                        
                        if consensus.consensus_confidence > 0.75:
                            current_quote = await self.market_data.get_real_time_quote(symbol)
                            
                            opportunity = TradeRecyclingOpportunity(
                                symbol=symbol,
                                strategy=RecyclingStrategy.MOMENTUM_PYRAMIDING,
                                original_trade_id=position["trade_id"],
                                recycling_confidence=consensus.consensus_confidence,
                                expected_additional_return=0.08,  # 8% expected return
                                risk_adjustment=1.2,  # Higher risk due to pyramiding
                                entry_price=current_quote.price,
                                position_size=int(position["size"] * 0.3),  # 30% additional size
                                reasoning=f"Momentum pyramiding: Level {position.get('pyramid_level', 0) + 1}",
                                technical_reset_score=momentum_score
                            )
                            opportunities.append(opportunity)
            
            return opportunities
            
        except Exception as e:
            self.logger.error(f"Error scanning momentum pyramiding: {e}")
            return []
    
    async def _scan_volatility_recycling(self, closed_positions: List[Dict]) -> List[TradeRecyclingOpportunity]:
        """Scan for volatility recycling opportunities"""
        try:
            # TODO: Implement volatility-based recycling strategies
            return []
            
        except Exception as e:
            self.logger.error(f"Error scanning volatility recycling: {e}")
            return []
    
    async def _analyze_technical_reset(self, symbol: str, data: pd.DataFrame) -> float:
        """Analyze if technical conditions have reset for re-entry"""
        try:
            if len(data) < 10:
                return 0.0
            
            # Calculate technical reset indicators
            reset_score = 0.0
            
            # RSI reset (from overbought back to neutral)
            current_rsi = data['rsi'].iloc[-1] if 'rsi' in data.columns else 50
            if 30 < current_rsi < 70:  # Neutral RSI
                reset_score += 0.3
            
            # Volume normalization
            recent_volume = data['volume'].tail(5).mean()
            avg_volume = data['volume'].mean()
            if recent_volume < avg_volume * 1.5:  # Volume has normalized
                reset_score += 0.2
            
            # Price consolidation
            recent_volatility = data['close'].tail(5).std()
            overall_volatility = data['close'].std()
            if recent_volatility < overall_volatility * 0.8:  # Lower volatility
                reset_score += 0.3
            
            # Support level hold
            support_level = data['low'].tail(10).min()
            current_price = data['close'].iloc[-1]
            if current_price > support_level * 1.02:  # Above support
                reset_score += 0.2
            
            return min(1.0, reset_score)
            
        except Exception as e:
            self.logger.error(f"Error analyzing technical reset: {e}")
            return 0.0
    
    def _calculate_momentum_score(self, data: pd.DataFrame) -> float:
        """Calculate momentum score for pyramiding decisions"""
        try:
            if len(data) < 10:
                return 0.0
            
            # Price momentum
            price_change = (data['close'].iloc[-1] - data['close'].iloc[-10]) / data['close'].iloc[-10]
            
            # Volume momentum
            recent_volume = data['volume'].tail(5).mean()
            previous_volume = data['volume'].tail(10).head(5).mean()
            volume_momentum = recent_volume / previous_volume if previous_volume > 0 else 1.0
            
            # Combine momentum factors
            momentum_score = (np.tanh(price_change * 10) + 1) / 2 * 0.7  # Price momentum (0-1)
            momentum_score += min(1.0, volume_momentum / 2) * 0.3  # Volume momentum
            
            return min(1.0, momentum_score)
            
        except Exception as e:
            self.logger.error(f"Error calculating momentum score: {e}")
            return 0.0
    
    def _filter_recycling_opportunities(self, opportunities: List[TradeRecyclingOpportunity], 
                                      account_metrics: Dict) -> List[TradeRecyclingOpportunity]:
        """Filter recycling opportunities based on risk and correlation constraints"""
        try:
            # Apply risk budget constraints
            total_recycling_risk = sum(op.risk_adjustment * op.position_size * op.entry_price 
                                     for op in opportunities)
            max_risk = account_metrics.get("total_value", 100000) * self.max_recycling_risk
            
            if total_recycling_risk > max_risk:
                # Sort by confidence and take top opportunities within risk budget
                sorted_ops = sorted(opportunities, key=lambda x: x.recycling_confidence, reverse=True)
                filtered_ops = []
                current_risk = 0.0
                
                for op in sorted_ops:
                    op_risk = op.risk_adjustment * op.position_size * op.entry_price
                    if current_risk + op_risk <= max_risk:
                        filtered_ops.append(op)
                        current_risk += op_risk
                
                return filtered_ops
            
            return opportunities
            
        except Exception as e:
            self.logger.error(f"Error filtering recycling opportunities: {e}")
            return opportunities
    
    def _rank_recycling_opportunities(self, opportunities: List[TradeRecyclingOpportunity]) -> List[TradeRecyclingOpportunity]:
        """Rank recycling opportunities by expected value and risk-adjusted returns"""
        try:
            # Calculate expected value for each opportunity
            for op in opportunities:
                perf = self.recycling_performance[op.strategy]
                expected_value = (perf["success_rate"] * op.expected_additional_return) - \
                               ((1 - perf["success_rate"]) * 0.03)  # Assume 3% loss on failures
                op.expected_value = expected_value / op.risk_adjustment  # Risk-adjusted EV
            
            # Sort by risk-adjusted expected value
            return sorted(opportunities, key=lambda x: getattr(x, 'expected_value', 0), reverse=True)
            
        except Exception as e:
            self.logger.error(f"Error ranking recycling opportunities: {e}")
            return opportunities
