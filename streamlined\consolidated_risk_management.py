"""
A.T.L.A.S CONSOLIDATED Risk Management Engine
MERGED: risk_management_engine.py + advanced_risk_controls.py
Comprehensive risk management with <PERSON> Criterion, dynamic trailing stops, smart exit sequencing
"""

import logging
import asyncio
import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from decimal import Decimal

from .config import settings
from .models import (
    RiskManagementProfile, Position, Quote, TechnicalIndicators,
    ChainOfThoughtAnalysis
)

class ExitTrigger(Enum):
    """Types of exit triggers"""
    STOP_LOSS = "stop_loss"
    TRAILING_STOP = "trailing_stop"
    PROFIT_TARGET = "profit_target"
    TIME_BASED = "time_based"
    TECHNICAL_SIGNAL = "technical_signal"
    RISK_LIMIT = "risk_limit"
    EARLY_CLOSURE = "early_closure"

@dataclass
class RiskParameters:
    """Risk management parameters for a position"""
    max_loss_percent: float = 2.0
    max_position_size: float = 0.20
    trailing_stop_percent: float = 0.05
    profit_target_r: float = 2.0
    time_stop_hours: int = 8
    confidence_threshold: float = 0.7
    kelly_fraction: float = 0.25

@dataclass
class ExitLevel:
    """Represents an exit level for smart sequencing"""
    level_name: str
    price: float
    quantity_percent: float
    trigger_type: ExitTrigger
    is_active: bool = True

@dataclass
class PositionRisk:
    """Enhanced position risk metrics"""
    symbol: str
    entry_price: float
    current_price: float
    quantity: int
    unrealized_pnl: float
    unrealized_pnl_percent: float
    risk_amount: float
    position_value: float
    account_percent: float
    time_held: timedelta
    confidence_score: float
    exit_levels: List[ExitLevel]

@dataclass
class PositionSizeCalculation:
    """Result of position sizing calculation"""
    recommended_shares: int
    dollar_amount: float
    risk_amount: float
    stop_loss_price: float
    target_price: float
    risk_reward_ratio: float
    kelly_fraction_used: float
    confidence_adjustment: float
    warnings: List[str]
    educational_explanation: str

@dataclass
class PreTradeValidation:
    """Pre-trade validation result"""
    is_valid: bool
    confidence_score: float
    risk_score: float
    warnings: List[str]
    blockers: List[str]
    educational_notes: List[str]
    recommendation: str

class ConsolidatedRiskManagementEngine:
    """
    CONSOLIDATED Risk Management Engine combining all risk management functionality
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Risk parameters
        self.risk_params = RiskParameters()
        
        # Active positions tracking
        self.active_positions: Dict[str, PositionRisk] = {}
        self.daily_pnl = 0.0
        self.daily_loss_limit = 0.03  # 3% daily loss limit
        self.session_start_time = datetime.utcnow()
        self.early_closure_triggered = False
        
        # Kelly Criterion constants
        self.KELLY_SAFETY_FACTOR = 0.25  # Use 25% of Kelly recommendation
        self.MAX_POSITION_SIZE = 0.20    # Maximum 20% of account per position
        self.MIN_POSITION_SIZE = 0.01    # Minimum 1% of account per position
        self.VIX_DANGER_THRESHOLD = 35   # Suspend trading above VIX 35
        
        # Performance tracking for Kelly calculation
        self.win_rate = 0.65
        self.avg_win = 0.08
        self.avg_loss = 0.04

    def calculate_kelly_position_size(self, account_size: float, confidence: float, 
                                    win_rate: float = None, avg_win: float = None, 
                                    avg_loss: float = None) -> float:
        """Calculate optimal position size using Kelly Criterion"""
        
        # Use provided values or defaults
        w = win_rate or self.win_rate
        avg_w = avg_win or self.avg_win
        avg_l = avg_loss or self.avg_loss
        
        # Kelly formula: f = (bp - q) / b
        b = avg_w / avg_l if avg_l > 0 else 2.0
        p = w
        q = 1 - w
        
        kelly_fraction = (b * p - q) / b
        
        # Apply confidence adjustment
        kelly_fraction *= confidence
        
        # Apply safety fraction
        kelly_fraction *= self.risk_params.kelly_fraction
        
        # Cap at maximum position size
        kelly_fraction = min(kelly_fraction, self.risk_params.max_position_size)
        
        # Ensure positive and reasonable
        kelly_fraction = max(0.01, min(kelly_fraction, 0.20))
        
        position_size = account_size * kelly_fraction
        
        self.logger.info(f"Kelly position size: ${position_size:,.2f} ({kelly_fraction:.1%} of account)")
        return position_size

    def calculate_position_size(self, 
                              symbol: str,
                              entry_price: float,
                              account_size: float,
                              confidence: float,
                              volatility: float = 0.02,
                              current_positions: int = 0) -> PositionSizeCalculation:
        """
        Calculate position size with Kelly Criterion and comprehensive risk management
        """
        
        warnings = []
        
        # Calculate stop loss (2% below entry for long positions)
        stop_loss_price = entry_price * (1 - self.risk_params.max_loss_percent / 100)
        
        # Calculate target price (2:1 risk/reward minimum)
        risk_per_share = entry_price - stop_loss_price
        target_price = entry_price + (risk_per_share * 2.0)
        
        # Kelly Criterion calculation
        avg_loss = abs(risk_per_share / entry_price)
        b = self.avg_win / avg_loss if avg_loss > 0 else 2.0
        p = self.win_rate
        q = 1 - self.win_rate
        
        kelly_fraction = (b * p - q) / b if b > 0 else 0.05
        
        # Apply safety factor and confidence adjustment
        confidence_adjustment = confidence * 0.8 + 0.2
        adjusted_kelly = kelly_fraction * self.KELLY_SAFETY_FACTOR * confidence_adjustment
        
        # Volatility adjustment
        vol_adjustment = 1.0 - min(volatility * 10, 0.5)
        adjusted_kelly *= vol_adjustment
        
        # Position concentration limits
        adjusted_kelly = min(adjusted_kelly, self.MAX_POSITION_SIZE)
        adjusted_kelly = max(adjusted_kelly, self.MIN_POSITION_SIZE)
        
        # Account for existing positions
        if current_positions >= 5:
            adjusted_kelly *= 0.8
            warnings.append("Reducing position size due to high number of existing positions")
        
        # Calculate dollar amount and shares
        dollar_amount = account_size * adjusted_kelly
        recommended_shares = int(dollar_amount / entry_price)
        actual_dollar_amount = recommended_shares * entry_price
        risk_amount = recommended_shares * risk_per_share
        
        # Risk/reward ratio
        potential_profit = recommended_shares * (target_price - entry_price)
        risk_reward_ratio = potential_profit / risk_amount if risk_amount > 0 else 0
        
        # Generate educational explanation
        explanation = self._generate_position_size_explanation(
            symbol, recommended_shares, actual_dollar_amount, risk_amount,
            adjusted_kelly, confidence, risk_reward_ratio
        )
        
        # Validation warnings
        if confidence < 0.7:
            warnings.append(f"Low confidence signal ({confidence*100:.0f}%) - consider reducing position")
        
        if risk_amount > account_size * 0.02:
            warnings.append(f"Risk amount (${risk_amount:.2f}) exceeds 2% of account")
        
        return PositionSizeCalculation(
            recommended_shares=recommended_shares,
            dollar_amount=actual_dollar_amount,
            risk_amount=risk_amount,
            stop_loss_price=stop_loss_price,
            target_price=target_price,
            risk_reward_ratio=risk_reward_ratio,
            kelly_fraction_used=adjusted_kelly,
            confidence_adjustment=confidence_adjustment,
            warnings=warnings,
            educational_explanation=explanation
        )

    def create_smart_exit_levels(self, entry_price: float, direction: str, 
                                confidence: float, volatility: float = 0.02) -> List[ExitLevel]:
        """Create smart exit sequencing levels"""
        
        exit_levels = []
        
        if direction.lower() == "long":
            # Stop loss
            stop_price = entry_price * (1 - self.risk_params.max_loss_percent / 100)
            exit_levels.append(ExitLevel(
                level_name="Stop Loss",
                price=stop_price,
                quantity_percent=1.0,
                trigger_type=ExitTrigger.STOP_LOSS
            ))
            
            # Trailing stop
            trailing_stop_price = entry_price * (1 - self.risk_params.trailing_stop_percent)
            exit_levels.append(ExitLevel(
                level_name="Trailing Stop",
                price=trailing_stop_price,
                quantity_percent=1.0,
                trigger_type=ExitTrigger.TRAILING_STOP
            ))
            
            # Smart exit sequencing based on confidence
            if confidence >= 0.8:
                # High confidence: Hold longer, scale out gradually
                r1_price = entry_price * (1 + volatility * 1.5)
                r2_price = entry_price * (1 + volatility * 3.0)
                r3_price = entry_price * (1 + volatility * 5.0)
                
                exit_levels.extend([
                    ExitLevel("1R Target", r1_price, 0.25, ExitTrigger.PROFIT_TARGET),
                    ExitLevel("2R Target", r2_price, 0.50, ExitTrigger.PROFIT_TARGET),
                    ExitLevel("3R+ Runner", r3_price, 0.25, ExitTrigger.PROFIT_TARGET)
                ])
            else:
                # Lower confidence: Take profits earlier
                r1_price = entry_price * (1 + volatility * 1.0)
                r2_price = entry_price * (1 + volatility * 2.0)
                
                exit_levels.extend([
                    ExitLevel("1R Target", r1_price, 0.50, ExitTrigger.PROFIT_TARGET),
                    ExitLevel("2R Target", r2_price, 0.50, ExitTrigger.PROFIT_TARGET)
                ])
        
        return exit_levels

    def _generate_position_size_explanation(self, symbol: str, shares: int, dollar_amount: float,
                                          risk_amount: float, kelly_fraction: float,
                                          confidence: float, risk_reward: float) -> str:
        """Generate educational explanation for position sizing"""
        
        explanation = f"""
🎯 **Position Sizing for {symbol}**

**Recommended Position**: {shares} shares (${dollar_amount:,.2f})
**Kelly Fraction Used**: {kelly_fraction:.1%} of account
**Risk Amount**: ${risk_amount:.2f}
**Risk/Reward Ratio**: {risk_reward:.1f}:1

**📚 Why This Size?**
• Kelly Criterion optimizes for long-term growth
• Confidence adjustment: {confidence:.0%} signal strength
• Safety factor applied to prevent over-leveraging
• Position sized to risk only 2% of account maximum

**🛡️ Risk Management**
• Stop loss automatically calculated
• Position size accounts for volatility
• Multiple exit levels planned for profit optimization
        """.strip()
        
        return explanation
