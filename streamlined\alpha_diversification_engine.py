"""
A.T.L.A.S Multi-Strategy Alpha Diversification Engine
Comprehensive strategy portfolio that expands beyond TTM Squeeze for superior returns
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from config import settings, POPULAR_SYMBOLS
from models import TechnicalIndicators, Quote
from market_data import MarketDataService
from technical_analysis import TechnicalScanner
from profit_maximization_engine import StrategyTier, ProfitOpportunity


class AlphaStrategy(Enum):
    """Alpha generation strategies"""
    MOMENTUM_BREAKOUT = "momentum_breakout"
    MEAN_REVERSION = "mean_reversion"
    VOLATILITY_EXPANSION = "volatility_expansion"
    VOLUME_SURGE = "volume_surge"
    SHORT_SQUEEZE = "short_squeeze"
    EARNINGS_MOMENTUM = "earnings_momentum"
    SECTOR_ROTATION = "sector_rotation"


@dataclass
class StrategySignal:
    """Individual strategy signal"""
    strategy: AlphaStrategy
    symbol: str
    signal_strength: float
    confidence: float
    expected_return: float
    risk_level: float
    timeframe: str
    entry_conditions: Dict[str, Any]
    exit_conditions: Dict[str, Any]


class AlphaDiversificationEngine:
    """
    Multi-strategy alpha generation engine that creates a diversified
    portfolio of trading strategies with EV-based filtering
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.market_data = MarketDataService()
        self.scanner = TechnicalScanner()
        
        # Strategy performance metrics (from backtesting)
        self.strategy_metrics = {
            AlphaStrategy.MOMENTUM_BREAKOUT: {
                "win_rate": 0.72, "avg_win": 0.095, "avg_loss": 0.032,
                "sharpe_ratio": 1.85, "max_drawdown": 0.12
            },
            AlphaStrategy.MEAN_REVERSION: {
                "win_rate": 0.68, "avg_win": 0.045, "avg_loss": 0.025,
                "sharpe_ratio": 1.45, "max_drawdown": 0.08
            },
            AlphaStrategy.VOLATILITY_EXPANSION: {
                "win_rate": 0.65, "avg_win": 0.12, "avg_loss": 0.045,
                "sharpe_ratio": 1.65, "max_drawdown": 0.15
            },
            AlphaStrategy.VOLUME_SURGE: {
                "win_rate": 0.78, "avg_win": 0.055, "avg_loss": 0.022,
                "sharpe_ratio": 2.1, "max_drawdown": 0.09
            },
            AlphaStrategy.SHORT_SQUEEZE: {
                "win_rate": 0.58, "avg_win": 0.18, "avg_loss": 0.055,
                "sharpe_ratio": 1.35, "max_drawdown": 0.22
            }
        }
        
        # Minimum expected value threshold for strategy execution
        self.min_ev_threshold = 0.02  # 2% minimum expected value
    
    async def generate_alpha_signals(self, symbols: Optional[List[str]] = None) -> List[StrategySignal]:
        """Generate diversified alpha signals across multiple strategies"""
        try:
            symbols = symbols or POPULAR_SYMBOLS[:50]  # Top 50 symbols for efficiency
            
            self.logger.info(f"🔍 Generating alpha signals for {len(symbols)} symbols...")
            
            all_signals = []
            
            # Generate signals from each strategy
            momentum_signals = await self._generate_momentum_breakout_signals(symbols)
            all_signals.extend(momentum_signals)
            
            reversion_signals = await self._generate_mean_reversion_signals(symbols)
            all_signals.extend(reversion_signals)
            
            volatility_signals = await self._generate_volatility_expansion_signals(symbols)
            all_signals.extend(volatility_signals)
            
            volume_signals = await self._generate_volume_surge_signals(symbols)
            all_signals.extend(volume_signals)
            
            squeeze_signals = await self._generate_short_squeeze_signals(symbols)
            all_signals.extend(squeeze_signals)
            
            # Filter by expected value
            positive_ev_signals = [
                signal for signal in all_signals 
                if self._calculate_signal_ev(signal) > self.min_ev_threshold
            ]
            
            # Rank by expected value and diversification
            ranked_signals = self._rank_signals_by_alpha(positive_ev_signals)
            
            self.logger.info(f"📈 Generated {len(ranked_signals)} positive EV alpha signals")
            return ranked_signals
            
        except Exception as e:
            self.logger.error(f"Error generating alpha signals: {e}")
            return []
    
    async def _generate_momentum_breakout_signals(self, symbols: List[str]) -> List[StrategySignal]:
        """Generate momentum breakout signals"""
        try:
            signals = []
            
            # Use existing breakout scanner
            breakout_results = await self.scanner.scan_breakout_patterns(symbols)
            
            for result in breakout_results:
                if result.score > 70:  # High-quality breakouts only
                    signal = StrategySignal(
                        strategy=AlphaStrategy.MOMENTUM_BREAKOUT,
                        symbol=result.symbol,
                        signal_strength=result.score / 100,
                        confidence=0.75,
                        expected_return=0.08,  # 8% expected return
                        risk_level=0.03,  # 3% risk
                        timeframe="daily",
                        entry_conditions={
                            "breakout_level": result.indicators.get("resistance_level", result.current_price),
                            "volume_confirmation": result.indicators.get("volume_ratio", 1.0) > 1.5
                        },
                        exit_conditions={
                            "stop_loss": result.current_price * 0.97,
                            "target": result.current_price * 1.08
                        }
                    )
                    signals.append(signal)
            
            return signals
            
        except Exception as e:
            self.logger.error(f"Error generating momentum breakout signals: {e}")
            return []
    
    async def _generate_mean_reversion_signals(self, symbols: List[str]) -> List[StrategySignal]:
        """Generate mean reversion signals"""
        try:
            signals = []
            
            # Use existing oversold scanner
            oversold_results = await self.scanner.scan_oversold_stocks(symbols)
            
            for result in oversold_results:
                if result.score > 75:  # Highly oversold only
                    signal = StrategySignal(
                        strategy=AlphaStrategy.MEAN_REVERSION,
                        symbol=result.symbol,
                        signal_strength=result.score / 100,
                        confidence=0.68,
                        expected_return=0.045,  # 4.5% expected return
                        risk_level=0.025,  # 2.5% risk
                        timeframe="swing",
                        entry_conditions={
                            "rsi_level": result.indicators.get("rsi", 30),
                            "support_level": result.indicators.get("support_level", result.current_price * 0.98)
                        },
                        exit_conditions={
                            "stop_loss": result.current_price * 0.975,
                            "target": result.current_price * 1.045
                        }
                    )
                    signals.append(signal)
            
            return signals
            
        except Exception as e:
            self.logger.error(f"Error generating mean reversion signals: {e}")
            return []
    
    async def _generate_volatility_expansion_signals(self, symbols: List[str]) -> List[StrategySignal]:
        """Generate volatility expansion signals (enhanced TTM Squeeze)"""
        try:
            signals = []
            
            # Use enhanced TTM Squeeze scanner
            squeeze_results = await self.scanner.scan_ttm_squeeze(symbols)
            
            for result in squeeze_results:
                if result.score > 80:  # Very tight squeezes only
                    signal = StrategySignal(
                        strategy=AlphaStrategy.VOLATILITY_EXPANSION,
                        symbol=result.symbol,
                        signal_strength=result.score / 100,
                        confidence=0.72,
                        expected_return=0.12,  # 12% expected return
                        risk_level=0.045,  # 4.5% risk
                        timeframe="daily",
                        entry_conditions={
                            "squeeze_ratio": result.indicators.get("squeeze_ratio", 0.8),
                            "volume_confirmation": result.indicators.get("volume_ratio", 1.0) > 2.0
                        },
                        exit_conditions={
                            "stop_loss": result.current_price * 0.955,
                            "target": result.current_price * 1.12
                        }
                    )
                    signals.append(signal)
            
            return signals
            
        except Exception as e:
            self.logger.error(f"Error generating volatility expansion signals: {e}")
            return []
    
    async def _generate_volume_surge_signals(self, symbols: List[str]) -> List[StrategySignal]:
        """Generate volume surge signals"""
        try:
            signals = []
            
            for symbol in symbols[:20]:  # Limit for performance
                try:
                    # Get recent data
                    data = await self.market_data.get_historical_data(symbol, "1Day", 20)
                    quote = await self.market_data.get_real_time_quote(symbol)
                    
                    if len(data) < 10:
                        continue
                    
                    # Calculate volume surge
                    avg_volume = data['volume'].tail(10).mean()
                    current_volume = quote.volume
                    
                    if current_volume > avg_volume * 3:  # 3x volume surge
                        signal = StrategySignal(
                            strategy=AlphaStrategy.VOLUME_SURGE,
                            symbol=symbol,
                            signal_strength=min(1.0, current_volume / avg_volume / 5),
                            confidence=0.78,
                            expected_return=0.055,  # 5.5% expected return
                            risk_level=0.022,  # 2.2% risk
                            timeframe="intraday",
                            entry_conditions={
                                "volume_ratio": current_volume / avg_volume,
                                "price_momentum": (quote.price - data['close'].iloc[-2]) / data['close'].iloc[-2]
                            },
                            exit_conditions={
                                "stop_loss": quote.price * 0.978,
                                "target": quote.price * 1.055
                            }
                        )
                        signals.append(signal)
                
                except Exception as e:
                    continue
            
            return signals
            
        except Exception as e:
            self.logger.error(f"Error generating volume surge signals: {e}")
            return []
    
    async def _generate_short_squeeze_signals(self, symbols: List[str]) -> List[StrategySignal]:
        """Generate short squeeze detection signals (placeholder)"""
        try:
            # TODO: Implement short squeeze detection using:
            # - Short interest data
            # - Float analysis
            # - Volume spikes
            # - Price momentum
            return []
            
        except Exception as e:
            self.logger.error(f"Error generating short squeeze signals: {e}")
            return []
    
    def _calculate_signal_ev(self, signal: StrategySignal) -> float:
        """Calculate expected value for a strategy signal"""
        try:
            metrics = self.strategy_metrics[signal.strategy]
            
            # Adjust metrics based on signal quality
            adjusted_win_rate = min(0.9, metrics["win_rate"] * (1 + signal.signal_strength * 0.15))
            adjusted_win_rate = min(0.95, adjusted_win_rate * (1 + signal.confidence * 0.1))
            
            # Expected Value = P(win) * Expected_Return - P(loss) * Risk_Level
            ev = (adjusted_win_rate * signal.expected_return) - ((1 - adjusted_win_rate) * signal.risk_level)
            
            return ev
            
        except Exception as e:
            self.logger.error(f"Error calculating signal EV: {e}")
            return 0.0
    
    def _rank_signals_by_alpha(self, signals: List[StrategySignal]) -> List[StrategySignal]:
        """Rank signals by alpha generation potential with diversification"""
        try:
            # Calculate EV for each signal
            for signal in signals:
                signal.expected_value = self._calculate_signal_ev(signal)
            
            # Sort by expected value descending
            ranked_signals = sorted(signals, key=lambda x: x.expected_value, reverse=True)
            
            # Apply diversification constraints
            diversified_signals = []
            strategy_counts = {}
            symbol_counts = {}
            
            for signal in ranked_signals:
                strategy_count = strategy_counts.get(signal.strategy, 0)
                symbol_count = symbol_counts.get(signal.symbol, 0)
                
                # Limit per strategy and per symbol
                if strategy_count < 3 and symbol_count < 1:  # Max 3 per strategy, 1 per symbol
                    diversified_signals.append(signal)
                    strategy_counts[signal.strategy] = strategy_count + 1
                    symbol_counts[signal.symbol] = symbol_count + 1
            
            return diversified_signals
            
        except Exception as e:
            self.logger.error(f"Error ranking signals: {e}")
            return signals
