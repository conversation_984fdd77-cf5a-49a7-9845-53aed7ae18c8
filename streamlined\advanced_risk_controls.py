"""
A.T.L.A.S Advanced Risk Controls System
Dynamic trailing stops, profit protection, and smart exit sequencing
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import math

class ExitTrigger(Enum):
    """Types of exit triggers"""
    STOP_LOSS = "stop_loss"
    TRAILING_STOP = "trailing_stop"
    PROFIT_TARGET = "profit_target"
    TIME_BASED = "time_based"
    TECHNICAL_SIGNAL = "technical_signal"
    RISK_LIMIT = "risk_limit"
    EARLY_CLOSURE = "early_closure"

@dataclass
class RiskParameters:
    """Risk management parameters for a position"""
    max_loss_percent: float = 2.0  # Maximum loss as % of account
    max_position_size: float = 0.20  # Maximum position size as % of account
    trailing_stop_percent: float = 0.05  # Trailing stop distance
    profit_target_r: float = 2.0  # Risk/reward ratio
    time_stop_hours: int = 8  # Maximum holding time
    confidence_threshold: float = 0.7  # Minimum confidence to enter
    kelly_fraction: float = 0.25  # Kelly Criterion fraction

@dataclass
class ExitLevel:
    """Represents an exit level for smart sequencing"""
    level_name: str
    price: float
    quantity_percent: float  # Percentage of position to exit
    trigger_type: ExitTrigger
    is_active: bool = True

@dataclass
class PositionRisk:
    """Risk metrics for a position"""
    symbol: str
    entry_price: float
    current_price: float
    quantity: int
    unrealized_pnl: float
    unrealized_pnl_percent: float
    risk_amount: float
    position_value: float
    account_percent: float
    time_held: timedelta
    confidence_score: float
    exit_levels: List[ExitLevel]

class AdvancedRiskControls:
    """
    Advanced risk management system with dynamic controls
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.active_positions: Dict[str, PositionRisk] = {}
        self.daily_pnl = 0.0
        self.daily_loss_limit = 0.03  # 3% daily loss limit
        self.session_start_time = datetime.utcnow()
        self.early_closure_triggered = False
        
        # Risk control settings
        self.risk_params = RiskParameters()
        
        # Performance tracking
        self.trade_history = []
        self.win_rate = 0.65  # Default win rate
        self.avg_win = 0.08  # Average win %
        self.avg_loss = 0.04  # Average loss %

    def calculate_kelly_position_size(self, account_size: float, confidence: float, 
                                    win_rate: float = None, avg_win: float = None, 
                                    avg_loss: float = None) -> float:
        """Calculate optimal position size using Kelly Criterion"""
        
        # Use provided values or defaults
        w = win_rate or self.win_rate
        avg_w = avg_win or self.avg_win
        avg_l = avg_loss or self.avg_loss
        
        # Kelly formula: f = (bp - q) / b
        # where b = avg_win/avg_loss, p = win_rate, q = 1 - win_rate
        b = avg_w / avg_l if avg_l > 0 else 2.0
        p = w
        q = 1 - w
        
        kelly_fraction = (b * p - q) / b
        
        # Apply confidence adjustment
        kelly_fraction *= confidence
        
        # Apply safety fraction (typically 25% of Kelly)
        kelly_fraction *= self.risk_params.kelly_fraction
        
        # Cap at maximum position size
        kelly_fraction = min(kelly_fraction, self.risk_params.max_position_size)
        
        # Ensure positive and reasonable
        kelly_fraction = max(0.01, min(kelly_fraction, 0.20))
        
        position_size = account_size * kelly_fraction
        
        self.logger.info(f"Kelly position size: ${position_size:,.2f} ({kelly_fraction:.1%} of account)")
        return position_size

    def create_smart_exit_levels(self, entry_price: float, direction: str, 
                                confidence: float, volatility: float = 0.02) -> List[ExitLevel]:
        """Create smart exit sequencing levels"""
        
        exit_levels = []
        
        if direction.lower() == "long":
            # Stop loss
            stop_price = entry_price * (1 - self.risk_params.max_loss_percent / 100)
            exit_levels.append(ExitLevel(
                level_name="Stop Loss",
                price=stop_price,
                quantity_percent=1.0,  # Exit entire position
                trigger_type=ExitTrigger.STOP_LOSS
            ))
            
            # Trailing stop (initially at stop loss level)
            trailing_stop_price = entry_price * (1 - self.risk_params.trailing_stop_percent)
            exit_levels.append(ExitLevel(
                level_name="Trailing Stop",
                price=trailing_stop_price,
                quantity_percent=1.0,
                trigger_type=ExitTrigger.TRAILING_STOP
            ))
            
            # Smart exit sequencing based on confidence
            if confidence >= 0.8:
                # High confidence: Hold longer, scale out gradually
                r1_price = entry_price * (1 + volatility * 1.5)  # 1.5R
                r2_price = entry_price * (1 + volatility * 3.0)  # 3R
                r3_price = entry_price * (1 + volatility * 5.0)  # 5R
                
                exit_levels.extend([
                    ExitLevel("1R Target", r1_price, 0.25, ExitTrigger.PROFIT_TARGET),
                    ExitLevel("2R Target", r2_price, 0.50, ExitTrigger.PROFIT_TARGET),
                    ExitLevel("3R+ Runner", r3_price, 0.25, ExitTrigger.PROFIT_TARGET)
                ])
            else:
                # Lower confidence: Take profits earlier
                r1_price = entry_price * (1 + volatility * 1.0)  # 1R
                r2_price = entry_price * (1 + volatility * 2.0)  # 2R
                
                exit_levels.extend([
                    ExitLevel("1R Target", r1_price, 0.50, ExitTrigger.PROFIT_TARGET),
                    ExitLevel("2R Target", r2_price, 0.50, ExitTrigger.PROFIT_TARGET)
                ])
        
        else:  # Short position
            # Similar logic for short positions (inverted)
            stop_price = entry_price * (1 + self.risk_params.max_loss_percent / 100)
            exit_levels.append(ExitLevel(
                level_name="Stop Loss",
                price=stop_price,
                quantity_percent=1.0,
                trigger_type=ExitTrigger.STOP_LOSS
            ))
            
            # Profit targets for short
            if confidence >= 0.8:
                r1_price = entry_price * (1 - volatility * 1.5)
                r2_price = entry_price * (1 - volatility * 3.0)
                
                exit_levels.extend([
                    ExitLevel("1R Target", r1_price, 0.33, ExitTrigger.PROFIT_TARGET),
                    ExitLevel("2R Target", r2_price, 0.67, ExitTrigger.PROFIT_TARGET)
                ])
        
        return exit_levels

    def update_trailing_stop(self, symbol: str, current_price: float) -> Optional[float]:
        """Update trailing stop for a position"""
        
        if symbol not in self.active_positions:
            return None
        
        position = self.active_positions[symbol]
        
        # Find trailing stop level
        trailing_stop = None
        for level in position.exit_levels:
            if level.trigger_type == ExitTrigger.TRAILING_STOP:
                trailing_stop = level
                break
        
        if not trailing_stop:
            return None
        
        # Calculate new trailing stop
        if current_price > position.entry_price:  # Profitable long position
            new_trailing_price = current_price * (1 - self.risk_params.trailing_stop_percent)
            
            # Only move trailing stop up (for long positions)
            if new_trailing_price > trailing_stop.price:
                old_price = trailing_stop.price
                trailing_stop.price = new_trailing_price
                
                self.logger.info(f"Updated trailing stop for {symbol}: ${old_price:.2f} -> ${new_trailing_price:.2f}")
                return new_trailing_price
        
        return trailing_stop.price

    def check_exit_triggers(self, symbol: str, current_price: float, 
                          current_time: datetime = None) -> List[Tuple[ExitLevel, str]]:
        """Check if any exit triggers are hit"""
        
        if symbol not in self.active_positions:
            return []
        
        position = self.active_positions[symbol]
        triggered_exits = []
        current_time = current_time or datetime.utcnow()
        
        # Update position metrics
        position.current_price = current_price
        position.unrealized_pnl = (current_price - position.entry_price) * position.quantity
        position.unrealized_pnl_percent = (current_price - position.entry_price) / position.entry_price
        position.time_held = current_time - self.session_start_time
        
        # Check each exit level
        for level in position.exit_levels:
            if not level.is_active:
                continue
            
            trigger_reason = None
            
            # Price-based triggers
            if level.trigger_type in [ExitTrigger.STOP_LOSS, ExitTrigger.TRAILING_STOP]:
                if current_price <= level.price:  # Long position stop hit
                    trigger_reason = f"Price ${current_price:.2f} hit {level.level_name} at ${level.price:.2f}"
            
            elif level.trigger_type == ExitTrigger.PROFIT_TARGET:
                if current_price >= level.price:  # Long position target hit
                    trigger_reason = f"Price ${current_price:.2f} hit {level.level_name} at ${level.price:.2f}"
            
            # Time-based trigger
            elif level.trigger_type == ExitTrigger.TIME_BASED:
                if position.time_held.total_seconds() > self.risk_params.time_stop_hours * 3600:
                    trigger_reason = f"Time stop: held for {position.time_held}"
            
            # Risk limit trigger
            elif level.trigger_type == ExitTrigger.RISK_LIMIT:
                if self.daily_pnl <= -self.daily_loss_limit:
                    trigger_reason = f"Daily loss limit hit: {self.daily_pnl:.1%}"
            
            if trigger_reason:
                triggered_exits.append((level, trigger_reason))
                level.is_active = False  # Deactivate triggered level
        
        return triggered_exits

    def check_early_session_closure(self) -> bool:
        """Check if early session closure should be triggered"""
        
        # Already triggered
        if self.early_closure_triggered:
            return True
        
        # Daily loss limit hit
        if self.daily_pnl <= -self.daily_loss_limit:
            self.early_closure_triggered = True
            self.logger.warning(f"Early session closure: Daily loss limit hit ({self.daily_pnl:.1%})")
            return True
        
        # Profit protection: Close early if significant profit achieved
        profit_protection_threshold = 0.05  # 5% daily profit
        if self.daily_pnl >= profit_protection_threshold:
            # Check if it's late in session (after 2 PM ET)
            current_hour = datetime.utcnow().hour
            if current_hour >= 18:  # 2 PM ET in UTC
                self.early_closure_triggered = True
                self.logger.info(f"Early session closure: Profit protection ({self.daily_pnl:.1%})")
                return True
        
        return False

    def calculate_dynamic_position_size(self, account_size: float, symbol: str, 
                                      confidence: float, volatility: float,
                                      current_positions: int = 0) -> Dict[str, Any]:
        """Calculate dynamic position size with multiple factors"""
        
        # Base Kelly calculation
        kelly_size = self.calculate_kelly_position_size(account_size, confidence)
        
        # Volatility adjustment
        vol_adjustment = 1.0 - min(volatility * 10, 0.5)  # Reduce size for high volatility
        adjusted_size = kelly_size * vol_adjustment
        
        # Position concentration limits
        max_single_position = account_size * self.risk_params.max_position_size
        adjusted_size = min(adjusted_size, max_single_position)
        
        # Account for existing positions
        if current_positions >= 5:
            adjusted_size *= 0.8  # Reduce size if many positions
        
        # Confidence scaling
        if confidence < 0.7:
            adjusted_size *= 0.5  # Half size for low confidence
        elif confidence > 0.9:
            adjusted_size *= 1.2  # Increase size for high confidence
        
        # Final safety check
        adjusted_size = max(adjusted_size, account_size * 0.01)  # Minimum 1%
        adjusted_size = min(adjusted_size, account_size * 0.20)  # Maximum 20%
        
        return {
            "position_size": adjusted_size,
            "kelly_size": kelly_size,
            "volatility_adjustment": vol_adjustment,
            "confidence_scaling": confidence,
            "position_percent": adjusted_size / account_size,
            "risk_amount": adjusted_size * self.risk_params.max_loss_percent / 100
        }

    def add_position(self, symbol: str, entry_price: float, quantity: int, 
                    confidence: float, direction: str = "long") -> str:
        """Add a new position to risk management"""
        
        # Create exit levels
        exit_levels = self.create_smart_exit_levels(entry_price, direction, confidence)
        
        # Create position risk object
        position = PositionRisk(
            symbol=symbol,
            entry_price=entry_price,
            current_price=entry_price,
            quantity=quantity,
            unrealized_pnl=0.0,
            unrealized_pnl_percent=0.0,
            risk_amount=abs(quantity * entry_price * self.risk_params.max_loss_percent / 100),
            position_value=abs(quantity * entry_price),
            account_percent=0.0,  # Will be calculated
            time_held=timedelta(0),
            confidence_score=confidence,
            exit_levels=exit_levels
        )
        
        self.active_positions[symbol] = position
        
        self.logger.info(f"Added position: {symbol} @ ${entry_price:.2f} x {quantity} shares")
        return f"Position added with {len(exit_levels)} exit levels"

    def get_risk_summary(self) -> Dict[str, Any]:
        """Get comprehensive risk summary"""
        
        total_risk = sum(pos.risk_amount for pos in self.active_positions.values())
        total_value = sum(pos.position_value for pos in self.active_positions.values())
        total_pnl = sum(pos.unrealized_pnl for pos in self.active_positions.values())
        
        return {
            "active_positions": len(self.active_positions),
            "total_risk_amount": total_risk,
            "total_position_value": total_value,
            "total_unrealized_pnl": total_pnl,
            "daily_pnl": self.daily_pnl,
            "daily_loss_limit": self.daily_loss_limit,
            "early_closure_triggered": self.early_closure_triggered,
            "risk_utilization": total_risk / (total_value * 0.03) if total_value > 0 else 0,
            "positions": {symbol: {
                "unrealized_pnl": pos.unrealized_pnl,
                "unrealized_pnl_percent": pos.unrealized_pnl_percent,
                "risk_amount": pos.risk_amount,
                "confidence": pos.confidence_score,
                "active_exits": len([l for l in pos.exit_levels if l.is_active])
            } for symbol, pos in self.active_positions.items()}
        }

    async def monitor_positions(self, market_data_callback):
        """Continuously monitor positions for exit triggers"""
        
        while self.active_positions and not self.early_closure_triggered:
            try:
                for symbol in list(self.active_positions.keys()):
                    # Get current market data
                    current_price = await market_data_callback(symbol)
                    
                    if current_price:
                        # Update trailing stops
                        self.update_trailing_stop(symbol, current_price)
                        
                        # Check exit triggers
                        triggered_exits = self.check_exit_triggers(symbol, current_price)
                        
                        for exit_level, reason in triggered_exits:
                            self.logger.info(f"Exit triggered for {symbol}: {reason}")
                            # Here you would execute the exit order
                            
                        # Check early closure
                        if self.check_early_session_closure():
                            self.logger.warning("Early session closure triggered - closing all positions")
                            break
                
                # Wait before next check
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in position monitoring: {e}")
                await asyncio.sleep(60)  # Wait longer on error
