"""
A.T.L.A.S Predicto API Integration
Enhanced market predictions using Predicto's deep learning forecasts
"""

import logging
import requests
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from config import settings


@dataclass
class PredictoForecast:
    """Predicto forecast data structure"""
    symbol: str
    date: str
    predictions: Dict[str, float]
    confidence_interval: Dict[str, float]
    trade_action: int  # 0=NoAction, 1=Buy, 2=Sell
    entry_price: float
    target_price: float
    stop_loss_price: float
    expected_change_pct: float
    avg_uncertainty: float
    model_avg_roi: float
    expiration_date: str


@dataclass
class NasdaqOutlook:
    """Nasdaq market outlook from Predicto"""
    date: str
    outlook_score: float  # Market feeling score
    forecasted_volatility: float  # Expected volatility
    models_uncertainty: float  # Model uncertainty level


class PredictoAPIIntegration:
    """
    Integration with Predicto API for enhanced market predictions
    Provides deep learning-based forecasts and market sentiment
    """
    
    def __init__(self, api_key: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        self.api_key = api_key or getattr(settings, 'PREDICTO_API_KEY', None)
        self.base_url = 'https://predic.to'
        
        if not self.api_key:
            self.logger.warning("Predicto API key not configured - predictions will be simulated")
            self.enabled = False
        else:
            self.enabled = True
            
        self.headers = {'X-Predicto-Api-Key': self.api_key} if self.api_key else {}
        
        # Cache for expensive operations
        self.cache: Dict[str, Any] = {}
        self.cache_ttl = 3600  # 1 hour cache
    
    def _validate_response(self, response: requests.Response) -> None:
        """Validate API response and raise exception on error"""
        if response.status_code != 200:
            raise Exception(f'Predicto API Error {response.status_code}: {response.text}')
    
    async def get_supported_tickers(self) -> List[str]:
        """Get list of tickers supported by Predicto"""
        
        if not self.enabled:
            # Return common tickers for simulation
            return ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META', 'NFLX']
        
        cache_key = "supported_tickers"
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if (datetime.now() - timestamp).seconds < self.cache_ttl:
                return cached_data
        
        try:
            endpoint = f"{self.base_url}/stocks/allwithforecast"
            response = requests.get(endpoint, headers=self.headers)
            self._validate_response(response)
            
            tickers_data = response.json()
            tickers = [item['RelatedSymbol'] for item in tickers_data]
            
            self.cache[cache_key] = (tickers, datetime.now())
            return tickers
            
        except Exception as e:
            self.logger.error(f"Error fetching supported tickers: {e}")
            return ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']  # Fallback
    
    async def get_forecast(self, symbol: str, date: Optional[str] = None) -> Optional[PredictoForecast]:
        """Get Predicto forecast for a specific symbol and date"""
        
        if date is None:
            date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        if not self.enabled:
            return self._generate_simulated_forecast(symbol, date)
        
        cache_key = f"forecast_{symbol}_{date}"
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if (datetime.now() - timestamp).seconds < self.cache_ttl:
                return cached_data
        
        try:
            # Get forecast data
            forecast_endpoint = f"{self.base_url}/api/forecasting/{symbol}/{date}/22"
            forecast_response = requests.get(forecast_endpoint, headers=self.headers)
            self._validate_response(forecast_response)
            
            forecast_json = forecast_response.json()
            if not forecast_json:
                return None
                
            predictions_json = forecast_json[0]['PredictionsJson']
            
            # Get trade pick data
            trade_pick_endpoint = f"{self.base_url}/api/forecasting/tradepicks/{symbol}/{date}/_,-100.0,0.0,0,0,modelroi"
            trade_pick_response = requests.get(trade_pick_endpoint, headers=self.headers)
            self._validate_response(trade_pick_response)
            
            trade_pick_json = trade_pick_response.json()
            trade_pick = trade_pick_json['Recommendations'][0] if trade_pick_json['Recommendations'] else None
            
            if not trade_pick:
                return None
            
            # Parse predictions
            predictions_df = pd.read_json(predictions_json, orient='index')
            
            # Create forecast object
            forecast = PredictoForecast(
                symbol=symbol,
                date=date,
                predictions=predictions_df['Prediction'].to_dict(),
                confidence_interval={
                    'high': predictions_df['HighConfInt'].to_dict(),
                    'low': predictions_df['LowConfInt'].to_dict()
                },
                trade_action=trade_pick['TradeAction'],
                entry_price=trade_pick['StartingPrice'],
                target_price=trade_pick['TargetSellPrice'],
                stop_loss_price=trade_pick['TargetStopLossPrice'],
                expected_change_pct=(trade_pick['TargetSellPrice'] - trade_pick['StartingPrice']) / trade_pick['StartingPrice'],
                avg_uncertainty=trade_pick.get('AvgUncertainty', 0.0),
                model_avg_roi=trade_pick.get('AverageROI', 0.0),
                expiration_date=trade_pick.get('ExpirationDate', '')
            )
            
            self.cache[cache_key] = (forecast, datetime.now())
            return forecast
            
        except Exception as e:
            self.logger.error(f"Error fetching forecast for {symbol}: {e}")
            return self._generate_simulated_forecast(symbol, date)
    
    async def get_nasdaq_outlook(self, days_back: int = 5) -> List[NasdaqOutlook]:
        """Get Nasdaq market outlook for the past N days"""
        
        since_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
        
        if not self.enabled:
            return self._generate_simulated_outlook(days_back)
        
        cache_key = f"nasdaq_outlook_{since_date}"
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if (datetime.now() - timestamp).seconds < self.cache_ttl:
                return cached_data
        
        try:
            outlooks = []
            
            # Get outlook score
            outlook_endpoint = f"{self.base_url}/api/forecasting/outlook/since/{since_date}/1,200"
            outlook_response = requests.get(outlook_endpoint, headers=self.headers)
            self._validate_response(outlook_response)
            outlook_data = outlook_response.json()
            
            # Get volatility
            volatility_endpoint = f"{self.base_url}/api/forecasting/outlook/since/{since_date}/201,200"
            volatility_response = requests.get(volatility_endpoint, headers=self.headers)
            self._validate_response(volatility_response)
            volatility_data = volatility_response.json()
            
            # Get uncertainty
            uncertainty_endpoint = f"{self.base_url}/api/forecasting/outlook/since/{since_date}/301,200"
            uncertainty_response = requests.get(uncertainty_endpoint, headers=self.headers)
            self._validate_response(uncertainty_response)
            uncertainty_data = uncertainty_response.json()
            
            # Combine data
            for i, outlook_item in enumerate(outlook_data):
                outlook = NasdaqOutlook(
                    date=outlook_item['Date'],
                    outlook_score=outlook_item['Value'],
                    forecasted_volatility=volatility_data[i]['Value'] if i < len(volatility_data) else 0.0,
                    models_uncertainty=uncertainty_data[i]['Value'] if i < len(uncertainty_data) else 0.0
                )
                outlooks.append(outlook)
            
            self.cache[cache_key] = (outlooks, datetime.now())
            return outlooks
            
        except Exception as e:
            self.logger.error(f"Error fetching Nasdaq outlook: {e}")
            return self._generate_simulated_outlook(days_back)
    
    def _generate_simulated_forecast(self, symbol: str, date: str) -> PredictoForecast:
        """Generate simulated forecast for testing when API is not available"""
        
        import random
        
        # Simulate realistic forecast data
        base_price = 150.0  # Simulated current price
        change_pct = random.uniform(-0.05, 0.05)  # -5% to +5%
        target_price = base_price * (1 + change_pct)
        stop_loss_price = base_price * (1 + change_pct * 0.5)  # Tighter stop loss
        
        return PredictoForecast(
            symbol=symbol,
            date=date,
            predictions={f"day_{i}": base_price * (1 + random.uniform(-0.02, 0.02)) for i in range(1, 16)},
            confidence_interval={
                'high': {f"day_{i}": base_price * (1 + random.uniform(0.01, 0.03)) for i in range(1, 16)},
                'low': {f"day_{i}": base_price * (1 + random.uniform(-0.03, -0.01)) for i in range(1, 16)}
            },
            trade_action=1 if change_pct > 0 else 2,  # Buy if positive, Sell if negative
            entry_price=base_price,
            target_price=target_price,
            stop_loss_price=stop_loss_price,
            expected_change_pct=change_pct,
            avg_uncertainty=random.uniform(0.05, 0.20),
            model_avg_roi=random.uniform(0.02, 0.15),
            expiration_date=(datetime.now() + timedelta(days=15)).strftime('%Y-%m-%d')
        )
    
    def _generate_simulated_outlook(self, days_back: int) -> List[NasdaqOutlook]:
        """Generate simulated Nasdaq outlook for testing"""
        
        import random
        
        outlooks = []
        for i in range(days_back):
            date = (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d')
            outlook = NasdaqOutlook(
                date=date,
                outlook_score=random.uniform(-1.0, 1.0),  # -1 to 1 scale
                forecasted_volatility=random.uniform(0.10, 0.30),  # 10% to 30%
                models_uncertainty=random.uniform(0.05, 0.25)  # 5% to 25%
            )
            outlooks.append(outlook)
        
        return outlooks
    
    async def get_enhanced_market_context(self, symbol: str) -> Dict[str, Any]:
        """Get enhanced market context combining Predicto predictions with current data"""
        
        try:
            # Get Predicto forecast
            forecast = await self.get_forecast(symbol)
            
            # Get Nasdaq outlook
            outlook = await self.get_nasdaq_outlook(days_back=3)
            
            # Combine into enhanced context
            context = {
                "predicto_enabled": self.enabled,
                "forecast": forecast.__dict__ if forecast else None,
                "nasdaq_outlook": [o.__dict__ for o in outlook],
                "market_sentiment": self._analyze_market_sentiment(outlook),
                "prediction_confidence": forecast.avg_uncertainty if forecast else 0.5,
                "ai_recommendation": self._generate_ai_recommendation(forecast, outlook)
            }
            
            return context
            
        except Exception as e:
            self.logger.error(f"Error getting enhanced market context: {e}")
            return {"predicto_enabled": False, "error": str(e)}
    
    def _analyze_market_sentiment(self, outlook: List[NasdaqOutlook]) -> str:
        """Analyze overall market sentiment from Nasdaq outlook"""
        
        if not outlook:
            return "neutral"
        
        avg_outlook = sum(o.outlook_score for o in outlook) / len(outlook)
        avg_uncertainty = sum(o.models_uncertainty for o in outlook) / len(outlook)
        
        if avg_outlook > 0.3 and avg_uncertainty < 0.15:
            return "bullish"
        elif avg_outlook < -0.3 and avg_uncertainty < 0.15:
            return "bearish"
        elif avg_uncertainty > 0.20:
            return "uncertain"
        else:
            return "neutral"
    
    def _generate_ai_recommendation(self, forecast: Optional[PredictoForecast], 
                                  outlook: List[NasdaqOutlook]) -> str:
        """Generate AI-enhanced recommendation based on Predicto data"""
        
        if not forecast:
            return "Insufficient prediction data - proceed with caution"
        
        sentiment = self._analyze_market_sentiment(outlook)
        
        if forecast.trade_action == 1:  # Buy
            if sentiment in ["bullish", "neutral"] and forecast.avg_uncertainty < 0.15:
                return f"Strong BUY signal - Predicto models show {forecast.expected_change_pct*100:.1f}% upside with low uncertainty"
            else:
                return f"Cautious BUY - Predicto suggests upside but market uncertainty is elevated"
        elif forecast.trade_action == 2:  # Sell
            if sentiment in ["bearish", "neutral"] and forecast.avg_uncertainty < 0.15:
                return f"Strong SELL signal - Predicto models predict {abs(forecast.expected_change_pct)*100:.1f}% downside"
            else:
                return f"Cautious SELL - Predicto suggests downside but market conditions are mixed"
        else:
            return "HOLD - Predicto models suggest no significant movement expected"
