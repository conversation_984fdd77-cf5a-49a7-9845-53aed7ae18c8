<!DOCTYPE html>
<html>
<head>
    <title>A.T.L.A.S Next-Generation Profit Optimization System</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #0c1426 0%, #1a2332 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 20px;
            background: rgba(255,255,255,0.05);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        .header h1 {
            font-size: 3em;
            background: linear-gradient(45deg, #00d4ff, #00ff88);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        .header p {
            font-size: 1.2em;
            color: #a0a0a0;
            margin-bottom: 20px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .status-card {
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
            transition: transform 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-5px);
        }
        .status-card h3 {
            color: #00ff88;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        .status-card ul {
            list-style: none;
        }
        .status-card li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        .status-card li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
        .api-section {
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        .api-section h2 {
            color: #00d4ff;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        .api-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 20px;
        }
        .api-button {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        .api-button:hover {
            background: linear-gradient(45deg, #00ff88, #00cc66);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,212,255,0.3);
        }
        .profit-optimizer {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }
        .profit-optimizer:hover {
            background: linear-gradient(45deg, #ff9ff3, #f368e0);
        }
        .test-section {
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        .test-section h3 {
            color: #00ff88;
            margin-bottom: 15px;
        }
        .test-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .form-group label {
            color: #a0a0a0;
            font-weight: 600;
        }
        .form-group input {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 16px;
        }
        .form-group input:focus {
            outline: none;
            border-color: #00d4ff;
            box-shadow: 0 0 10px rgba(0,212,255,0.3);
        }
        #result {
            background: rgba(0,0,0,0.3);
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid rgba(255,255,255,0.1);
        }
        .chat-section {
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        .chat-section h3 {
            color: #00ff88;
            margin-bottom: 20px;
        }
        .chat-container {
            background: rgba(0,0,0,0.2);
            border-radius: 12px;
            border: 1px solid rgba(255,255,255,0.1);
            overflow: hidden;
        }
        .chat-messages {
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 12px;
            line-height: 1.5;
        }
        .user-message {
            align-self: flex-end;
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
        }
        .assistant-message {
            align-self: flex-start;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .message-content {
            word-wrap: break-word;
        }
        .message-content ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .message-content li {
            margin: 5px 0;
        }
        .chat-input-container {
            display: flex;
            padding: 15px;
            background: rgba(255,255,255,0.05);
            border-top: 1px solid rgba(255,255,255,0.1);
        }
        #chatInput {
            flex: 1;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 16px;
            margin-right: 10px;
        }
        #chatInput:focus {
            outline: none;
            border-color: #00d4ff;
            box-shadow: 0 0 10px rgba(0,212,255,0.3);
        }
        #chatInput::placeholder {
            color: rgba(255,255,255,0.5);
        }
        #sendButton {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            color: white;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        #sendButton:hover {
            background: linear-gradient(45deg, #00ff88, #00cc66);
            transform: translateY(-2px);
        }
        #sendButton:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* Quick Actions */
        .quick-actions {
            display: flex;
            gap: 10px;
            padding: 15px;
            background: rgba(255,255,255,0.03);
            border-top: 1px solid rgba(255,255,255,0.1);
            flex-wrap: wrap;
        }
        .quick-btn {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 20px;
            padding: 8px 16px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .quick-btn:hover {
            background: rgba(0,212,255,0.2);
            border-color: #00d4ff;
            transform: translateY(-2px);
        }
        .profit-btn {
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            border-color: #ff6b35;
        }
        .profit-btn:hover {
            background: linear-gradient(45deg, #f7931e, #ff6b35);
        }

        /* Market Widget */
        .market-widget {
            background: rgba(255,255,255,0.05);
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: 12px;
            margin: 15px;
            overflow: hidden;
        }
        .widget-header {
            background: rgba(0,212,255,0.2);
            padding: 10px 15px;
            font-weight: bold;
            font-size: 14px;
            color: #00d4ff;
        }
        .widget-content {
            padding: 15px;
        }
        .market-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .market-item:last-child {
            border-bottom: none;
        }
        .symbol {
            font-weight: bold;
            color: #00d4ff;
            min-width: 50px;
        }
        .price {
            font-weight: bold;
            color: white;
        }
        .change {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
        }
        .change.positive {
            background: rgba(34,197,94,0.2);
            color: #22c55e;
        }
        .change.negative {
            background: rgba(239,68,68,0.2);
            color: #ef4444;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 A.T.L.A.S</h1>
            <p>Next-Generation Profit Optimization System</p>
            <p><strong>Advanced Trading & Learning Analysis System</strong></p>
            <div style="margin-top: 20px; padding: 15px; background: rgba(0,255,136,0.1); border-radius: 10px; border: 1px solid rgba(0,255,136,0.3);">
                <strong>🎯 Status: FULLY OPERATIONAL</strong><br>
                📚 Educational Paper Trading Mode | 🤖 AI-Enhanced Intelligence | 🔄 Profit Optimization Active
            </div>
        </div>

        <div class="status-grid">
            <div class="status-card">
                <h3>🎯 Profit Optimization Features</h3>
                <ul>
                    <li>Enhanced TTM Squeeze Analysis</li>
                    <li>Multi-Strategy Alpha Generation</li>
                    <li>Intelligent Trade Recycling</li>
                    <li>Dynamic Risk Management</li>
                    <li>Real-time RL Optimization</li>
                </ul>
            </div>
            <div class="status-card">
                <h3>🤖 AI Integration Status</h3>
                <ul>
                    <li>OpenAI GPT-4 Active</li>
                    <li>Predicto API Integrated</li>
                    <li>Multi-Agent Coordination</li>
                    <li>Chain-of-Thought Analysis</li>
                    <li>Educational Insights</li>
                </ul>
            </div>
            <div class="status-card">
                <h3>📊 Market Data Sources</h3>
                <ul>
                    <li>Alpaca Markets API</li>
                    <li>Financial Modeling Prep</li>
                    <li>Predicto Deep Learning</li>
                    <li>Real-time Quote Data</li>
                    <li>Historical Analysis</li>
                </ul>
            </div>
        </div>

        <div class="api-section">
            <h2>🚀 API Endpoints</h2>
            <p>Access A.T.L.A.S powerful trading intelligence through these endpoints:</p>
            <div class="api-buttons">
                <a href="/docs" class="api-button">📖 API Documentation</a>
                <a href="/api/v1/health" class="api-button">🔍 Health Check</a>
                <button onclick="testProfitOptimization()" class="api-button profit-optimizer">🎯 Test Profit Optimization</button>
                <a href="/api/v1/cot/dashboard" class="api-button">📊 CoT Dashboard</a>
            </div>
        </div>

        <div class="chat-section">
            <h3>💬 Chat with A.T.L.A.S AI Trading Assistant</h3>
            <div class="chat-container">
                <div id="chatMessages" class="chat-messages">
                    <div class="message assistant-message">
                        <div class="message-content">
                            <strong>🤖 A.T.L.A.S:</strong> Hello! I'm your AI trading assistant. I can help you with:
                            <ul>
                                <li>📈 Market analysis and trading strategies</li>
                                <li>💰 Profit optimization plans</li>
                                <li>🎓 Trading education and explanations</li>
                                <li>⚠️ Risk management advice</li>
                                <li>📊 Technical analysis insights</li>
                            </ul>
                            What would you like to explore today?
                        </div>
                    </div>
                </div>

                <!-- Quick Action Buttons -->
                <div class="quick-actions">
                    <button onclick="quickAction('Get a stock quote')" class="quick-btn">📊 Get Quote</button>
                    <button onclick="quickAction('Show technical indicators')" class="quick-btn">📈 Technical Analysis</button>
                    <button onclick="quickAction('Analyze trends')" class="quick-btn">📊 Market Trends</button>
                    <button onclick="quickAction('Make me $100 today')" class="quick-btn profit-btn">💰 Profit Plan</button>
                </div>

                <!-- Market Data Widget -->
                <div class="market-widget" id="marketWidget">
                    <div class="widget-header">Market Overview</div>
                    <div class="widget-content">
                        <div class="market-item">
                            <span class="symbol">SPY</span>
                            <span class="price" id="spyPrice">Loading...</span>
                            <span class="change" id="spyChange">--</span>
                        </div>
                        <div class="market-item">
                            <span class="symbol">QQQ</span>
                            <span class="price" id="qqqPrice">Loading...</span>
                            <span class="change" id="qqqChange">--</span>
                        </div>
                    </div>
                </div>

                <div class="chat-input-container">
                    <input type="text" id="chatInput" placeholder="Ask me anything about trading... (e.g., 'Help me make $50 today')" />
                    <button onclick="sendMessage()" id="sendButton">
                        <span id="sendIcon">📤</span>
                        <span id="loadingIcon" style="display: none;">⏳</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Quick Test: Profit Optimization System</h3>
            <div class="test-form">
                <div class="form-group">
                    <label for="accountSize">Account Size ($)</label>
                    <input type="number" id="accountSize" value="100000" min="1000" step="1000">
                </div>
                <button onclick="testProfitOptimization()" class="api-button profit-optimizer">
                    🚀 Generate Profit Optimization Plan
                </button>
            </div>
            <div id="result"></div>
        </div>
    </div>

    <script>
        // Advanced A.T.L.A.S Chat functionality with intelligent routing
        async function sendMessage() {
            const chatInput = document.getElementById('chatInput');
            const message = chatInput.value.trim();

            if (!message) return;

            // Add user message to chat
            addMessageToChat('user', message);
            chatInput.value = '';

            // Show loading state
            setLoadingState(true);

            try {
                // Intelligent routing based on message content
                const route = determineRoute(message);
                let response;

                switch (route.type) {
                    case 'profit_optimization':
                        response = await handleProfitOptimization(message, route.params);
                        break;
                    case 'symbol_analysis':
                        response = await handleSymbolAnalysis(message, route.params);
                        break;
                    case 'trading_plan':
                        response = await handleTradingPlan(message, route.params);
                        break;
                    case 'market_data':
                        response = await handleMarketData(message, route.params);
                        break;
                    case 'technical_scan':
                        response = await handleTechnicalScan(message, route.params);
                        break;
                    case 'portfolio_check':
                        response = await handlePortfolioCheck(message);
                        break;
                    case 'education':
                        response = await handleEducation(message);
                        break;
                    default:
                        response = await handleGeneralChat(message);
                }

                // Display the response
                if (response.success) {
                    addMessageToChat('assistant', response.message);

                    // Add any additional data visualizations
                    if (response.data) {
                        displayAdditionalData(response.data);
                    }
                } else {
                    addMessageToChat('assistant', `❌ ${response.error || 'I encountered an issue processing your request.'}`);
                }

            } catch (error) {
                addMessageToChat('assistant', `❌ Network error: ${error.message}`);
            } finally {
                setLoadingState(false);
            }
        }

        function addMessageToChat(type, content) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            if (type === 'user') {
                messageContent.innerHTML = `<strong>👤 You:</strong> ${content}`;
            } else {
                messageContent.innerHTML = `<strong>🤖 A.T.L.A.S:</strong> ${content.replace(/\n/g, '<br>')}`;
            }

            messageDiv.appendChild(messageContent);
            chatMessages.appendChild(messageDiv);

            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function setLoadingState(isLoading) {
            const sendButton = document.getElementById('sendButton');
            const sendIcon = document.getElementById('sendIcon');
            const loadingIcon = document.getElementById('loadingIcon');
            const chatInput = document.getElementById('chatInput');

            sendButton.disabled = isLoading;
            chatInput.disabled = isLoading;

            if (isLoading) {
                sendIcon.style.display = 'none';
                loadingIcon.style.display = 'inline';
                addMessageToChat('assistant', '🤔 Thinking...');
            } else {
                sendIcon.style.display = 'inline';
                loadingIcon.style.display = 'none';
                // Remove the "Thinking..." message
                const messages = document.querySelectorAll('.message');
                const lastMessage = messages[messages.length - 1];
                if (lastMessage && lastMessage.textContent.includes('🤔 Thinking...')) {
                    lastMessage.remove();
                }
            }
        }

        // Intelligent routing system
        function determineRoute(message) {
            const msg = message.toLowerCase();

            // Profit optimization patterns
            if (msg.includes('make me') && (msg.includes('$') || msg.includes('money') || msg.includes('profit'))) {
                const amount = extractAmount(message);
                return { type: 'profit_optimization', params: { goal: message, amount } };
            }

            // Symbol analysis patterns
            const symbolMatch = message.match(/\b[A-Z]{1,5}\b/);
            if (symbolMatch && (msg.includes('analyze') || msg.includes('look at') || msg.includes('check') || msg.includes('what') || msg.includes('how'))) {
                return { type: 'symbol_analysis', params: { symbol: symbolMatch[0] } };
            }

            // Trading plan patterns
            if (msg.includes('plan') || msg.includes('strategy') || msg.includes('trade') || msg.includes('position')) {
                return { type: 'trading_plan', params: { request: message } };
            }

            // Market data patterns
            if (msg.includes('quote') || msg.includes('price') || msg.includes('market') || msg.includes('data')) {
                return { type: 'market_data', params: { request: message } };
            }

            // Technical scan patterns
            if (msg.includes('scan') || msg.includes('find') || msg.includes('search') || msg.includes('momentum') || msg.includes('breakout')) {
                return { type: 'technical_scan', params: { request: message } };
            }

            // Portfolio patterns
            if (msg.includes('portfolio') || msg.includes('positions') || msg.includes('account') || msg.includes('balance')) {
                return { type: 'portfolio_check', params: {} };
            }

            // Education patterns
            if (msg.includes('explain') || msg.includes('learn') || msg.includes('teach') || msg.includes('how to') || msg.includes('what is')) {
                return { type: 'education', params: { question: message } };
            }

            return { type: 'general_chat', params: { message } };
        }

        function extractAmount(message) {
            const match = message.match(/\$(\d+(?:,\d{3})*(?:\.\d{2})?)/);
            return match ? parseFloat(match[1].replace(/,/g, '')) : null;
        }

        // Handler functions for different request types
        async function handleProfitOptimization(message, params) {
            try {
                const response = await fetch('/api/v1/profit-optimization', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        account_size: 50000, // Default account size
                        active_positions: [],
                        closed_positions: []
                    })
                });

                const data = await response.json();

                if (data.success) {
                    let message = `🎯 **Profit Optimization Plan Generated!**\n\n`;
                    message += `💰 **Target**: ${params.goal}\n`;
                    message += `📊 **Strategy**: ${data.strategy_summary}\n`;
                    message += `⚡ **Top Opportunities**:\n`;

                    data.opportunities.slice(0, 3).forEach((opp, i) => {
                        message += `${i + 1}. **${opp.symbol}** - ${opp.setup_type} (${opp.confidence}% confidence)\n`;
                        message += `   💡 ${opp.reasoning}\n`;
                    });

                    message += `\n🛡️ **Risk Management**: ${data.risk_summary}`;

                    return { success: true, message, data: data.opportunities };
                } else {
                    return { success: false, error: data.error };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function handleSymbolAnalysis(message, params) {
            try {
                const response = await fetch('/api/v1/cot/analyze-symbol', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        symbol: params.symbol,
                        account_size: 50000
                    })
                });

                const data = await response.json();

                if (data.success) {
                    let message = `📈 **${params.symbol} Analysis Complete!**\n\n`;
                    message += `🎯 **Signal**: ${data.signal} (${data.confidence}% confidence)\n`;
                    message += `💭 **Chain of Thought**:\n${data.reasoning}\n\n`;
                    message += `📊 **Technical Summary**: ${data.technical_summary}\n`;
                    message += `💰 **Position Recommendation**: ${data.position_recommendation}\n`;
                    message += `🛡️ **Risk Level**: ${data.risk_assessment}`;

                    return { success: true, message, data: data.technical_data };
                } else {
                    return { success: false, error: data.error };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function handleTradingPlan(message, params) {
            try {
                const response = await fetch('/api/v1/cot/create-plan', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        user_request: params.request,
                        account_size: 50000,
                        risk_tolerance: 'moderate'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    let message = `📋 **Trading Plan Created!**\n\n`;
                    message += `🎯 **Objective**: ${data.objective}\n`;
                    message += `💰 **Capital Allocation**: ${data.capital_allocation}\n`;
                    message += `📊 **Strategy**: ${data.strategy_summary}\n`;
                    message += `🛡️ **Risk Management**: ${data.risk_rules}\n\n`;
                    message += `⚡ **Immediate Actions**:\n`;
                    data.action_items.forEach((action, i) => {
                        message += `${i + 1}. ${action}\n`;
                    });

                    return { success: true, message, data: data.detailed_plan };
                } else {
                    return { success: false, error: data.error };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function handleMarketData(message, params) {
            try {
                // Extract symbol if present
                const symbolMatch = message.match(/\b[A-Z]{1,5}\b/);
                if (symbolMatch) {
                    const symbol = symbolMatch[0];
                    const response = await fetch(`/api/v1/quote/${symbol}`);
                    const data = await response.json();

                    let message = `📊 **${symbol} Market Data**\n\n`;
                    message += `💰 **Price**: $${data.price} (${data.change >= 0 ? '+' : ''}${data.change_percent}%)\n`;
                    message += `📈 **High**: $${data.high} | **Low**: $${data.low}\n`;
                    message += `📊 **Volume**: ${data.volume.toLocaleString()}\n`;
                    message += `⏰ **Last Updated**: ${new Date(data.timestamp).toLocaleTimeString()}`;

                    return { success: true, message, data };
                } else {
                    return { success: true, message: "📊 Please specify a stock symbol to get market data (e.g., 'Get AAPL price')" };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function handleTechnicalScan(message, params) {
            try {
                const response = await fetch('/api/v1/scan', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        scan_type: 'comprehensive',
                        symbols: ['SPY', 'QQQ', 'AAPL', 'TSLA', 'MSFT', 'NVDA']
                    })
                });

                const data = await response.json();

                let message = `🔍 **Technical Scan Results**\n\n`;
                message += `⚡ **Momentum Plays**:\n`;
                data.momentum_plays.slice(0, 3).forEach(stock => {
                    message += `• **${stock.symbol}** - ${stock.signal} (${stock.score}/10)\n`;
                });

                message += `\n📈 **Breakout Candidates**:\n`;
                data.breakout_candidates.slice(0, 3).forEach(stock => {
                    message += `• **${stock.symbol}** - ${stock.pattern} (${stock.probability}%)\n`;
                });

                message += `\n🎯 **TTM Squeeze Setups**:\n`;
                data.ttm_squeeze.slice(0, 3).forEach(stock => {
                    message += `• **${stock.symbol}** - ${stock.stage} (${stock.timeframe})\n`;
                });

                return { success: true, message, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function handlePortfolioCheck(message) {
            try {
                const [portfolioResponse, positionsResponse] = await Promise.all([
                    fetch('/api/v1/portfolio'),
                    fetch('/api/v1/positions')
                ]);

                const portfolio = await portfolioResponse.json();
                const positions = await positionsResponse.json();

                let message = `💼 **Portfolio Summary**\n\n`;
                message += `💰 **Account Value**: $${portfolio.total_value.toLocaleString()}\n`;
                message += `📈 **Day P&L**: ${portfolio.day_pnl >= 0 ? '+' : ''}$${portfolio.day_pnl.toLocaleString()}\n`;
                message += `📊 **Buying Power**: $${portfolio.buying_power.toLocaleString()}\n\n`;

                if (positions.length > 0) {
                    message += `🎯 **Active Positions**:\n`;
                    positions.forEach(pos => {
                        message += `• **${pos.symbol}**: ${pos.qty} shares @ $${pos.avg_cost} (${pos.unrealized_pnl >= 0 ? '+' : ''}$${pos.unrealized_pnl})\n`;
                    });
                } else {
                    message += `📝 **No active positions** - Ready for new opportunities!`;
                }

                return { success: true, message, data: { portfolio, positions } };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function handleEducation(message) {
            try {
                const response = await fetch('/api/v1/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: message,
                        context: {
                            mode: 'educational',
                            interface: 'atlas_html'
                        }
                    })
                });

                const data = await response.json();

                let responseMessage = `🎓 **Educational Response**\n\n`;
                responseMessage += data.response;

                if (data.book_references) {
                    responseMessage += `\n\n📚 **Related Reading**:\n`;
                    data.book_references.forEach(ref => {
                        responseMessage += `• ${ref.book}: "${ref.quote}"\n`;
                    });
                }

                return { success: true, message: responseMessage, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function handleGeneralChat(message) {
            try {
                const response = await fetch('/api/v1/chat/cot', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: message
                    })
                });

                const data = await response.json();

                return {
                    success: true,
                    message: data.response || "I understand your message. How can I help you with trading today?",
                    data
                };
            } catch (error) {
                // Fallback to basic chat
                try {
                    const fallbackResponse = await fetch('/api/v1/chat', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            message: message,
                            context: { interface: 'atlas_html' }
                        })
                    });

                    const fallbackData = await fallbackResponse.json();
                    return { success: true, message: fallbackData.response, data: fallbackData };
                } catch (fallbackError) {
                    return { success: false, error: fallbackError.message };
                }
            }
        }

        function displayAdditionalData(data) {
            // This function can be expanded to show charts, tables, etc.
            // For now, we'll just log the data for debugging
            console.log('Additional data:', data);
        }

        // Quick action function
        function quickAction(message) {
            document.getElementById('chatInput').value = message;
            sendMessage();
        }

        // Load market data for widget
        async function loadMarketData() {
            try {
                const symbols = ['SPY', 'QQQ'];
                for (const symbol of symbols) {
                    try {
                        const response = await fetch(`/api/v1/quote/${symbol}`);
                        const data = await response.json();

                        const priceElement = document.getElementById(`${symbol.toLowerCase()}Price`);
                        const changeElement = document.getElementById(`${symbol.toLowerCase()}Change`);

                        if (priceElement && changeElement) {
                            priceElement.textContent = `$${data.price}`;
                            const changePercent = data.change_percent;
                            changeElement.textContent = `${changePercent >= 0 ? '+' : ''}${changePercent}%`;
                            changeElement.className = `change ${changePercent >= 0 ? 'positive' : 'negative'}`;
                        }
                    } catch (error) {
                        console.log(`Error loading ${symbol} data:`, error);
                    }
                }
            } catch (error) {
                console.log('Error loading market data:', error);
            }
        }

        // Initialize market data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadMarketData();
            // Refresh market data every 30 seconds
            setInterval(loadMarketData, 30000);
        });

        // Handle Enter key in chat input
        document.addEventListener('DOMContentLoaded', function() {
            const chatInput = document.getElementById('chatInput');
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        });

        // Profit optimization test function
        async function testProfitOptimization() {
            const accountSize = document.getElementById('accountSize').value;
            const resultDiv = document.getElementById('result');

            resultDiv.innerHTML = '🔄 Generating profit optimization plan...';

            try {
                const response = await fetch('/api/v1/profit-optimization', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        account_size: parseFloat(accountSize),
                        active_positions: [],
                        closed_positions: []
                    })
                });

                const data = await response.json();

                if (data.success) {
                    const allocation = data.optimized_allocation || {};
                    const riskMetrics = data.risk_metrics || {};

                    resultDiv.innerHTML = `✅ SUCCESS! Profit Optimization Plan Generated

🎯 PLAN SUMMARY:
• Account Size: $${parseFloat(accountSize).toLocaleString()}
• Total Allocation: $${(allocation.total_allocation || 0).toLocaleString()}
• Allocation Percentage: ${(allocation.allocation_percentage || 0).toFixed(1)}%
• Diversification: ${allocation.diversification_count || 0} positions

📊 OPPORTUNITIES FOUND:
• Profit Opportunities: ${(data.profit_opportunities || []).length}
• Alpha Signals: ${(data.alpha_signals || []).length}
• Recycling Opportunities: ${(data.recycling_opportunities || []).length}

🛡️ RISK METRICS:
• Max Portfolio Risk: $${(riskMetrics.max_portfolio_risk || 0).toLocaleString()}
• Diversification Score: ${(riskMetrics.diversification_score || 0).toFixed(2)}
• Expected Return: ${((riskMetrics.expected_portfolio_return || 0) * 100).toFixed(1)}%

🎓 EDUCATIONAL INSIGHTS:
${(data.educational_insights || []).map((insight, i) => `${i + 1}. ${insight}`).join('\n')}

🎉 A.T.L.A.S Next-Generation Profit Optimization System is working perfectly!`;
                } else {
                    resultDiv.innerHTML = `❌ Error: ${data.error || 'Unknown error occurred'}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ Network Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
