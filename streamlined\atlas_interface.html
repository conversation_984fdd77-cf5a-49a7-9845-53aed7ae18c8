<!DOCTYPE html>
<html>
<head>
    <title>A.T.L.A.S Next-Generation Profit Optimization System</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #0c1426 0%, #1a2332 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 20px;
            background: rgba(255,255,255,0.05);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        .header h1 {
            font-size: 3em;
            background: linear-gradient(45deg, #00d4ff, #00ff88);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        .header p {
            font-size: 1.2em;
            color: #a0a0a0;
            margin-bottom: 20px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .status-card {
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
            transition: transform 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-5px);
        }
        .status-card h3 {
            color: #00ff88;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        .status-card ul {
            list-style: none;
        }
        .status-card li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        .status-card li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
        .api-section {
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        .api-section h2 {
            color: #00d4ff;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        .api-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 20px;
        }
        .api-button {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        .api-button:hover {
            background: linear-gradient(45deg, #00ff88, #00cc66);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,212,255,0.3);
        }
        .profit-optimizer {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }
        .profit-optimizer:hover {
            background: linear-gradient(45deg, #ff9ff3, #f368e0);
        }
        .test-section {
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        .test-section h3 {
            color: #00ff88;
            margin-bottom: 15px;
        }
        .test-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .form-group label {
            color: #a0a0a0;
            font-weight: 600;
        }
        .form-group input {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 16px;
        }
        .form-group input:focus {
            outline: none;
            border-color: #00d4ff;
            box-shadow: 0 0 10px rgba(0,212,255,0.3);
        }
        #result {
            background: rgba(0,0,0,0.3);
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid rgba(255,255,255,0.1);
        }
        .chat-section {
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        .chat-section h3 {
            color: #00ff88;
            margin-bottom: 20px;
        }
        .chat-container {
            background: rgba(0,0,0,0.2);
            border-radius: 12px;
            border: 1px solid rgba(255,255,255,0.1);
            overflow: hidden;
        }
        .chat-messages {
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 12px;
            line-height: 1.5;
        }
        .user-message {
            align-self: flex-end;
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
        }
        .assistant-message {
            align-self: flex-start;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .message-content {
            word-wrap: break-word;
        }
        .message-content ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .message-content li {
            margin: 5px 0;
        }
        .chat-input-container {
            display: flex;
            padding: 15px;
            background: rgba(255,255,255,0.05);
            border-top: 1px solid rgba(255,255,255,0.1);
        }
        #chatInput {
            flex: 1;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 16px;
            margin-right: 10px;
        }
        #chatInput:focus {
            outline: none;
            border-color: #00d4ff;
            box-shadow: 0 0 10px rgba(0,212,255,0.3);
        }
        #chatInput::placeholder {
            color: rgba(255,255,255,0.5);
        }
        #sendButton {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            color: white;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        #sendButton:hover {
            background: linear-gradient(45deg, #00ff88, #00cc66);
            transform: translateY(-2px);
        }
        #sendButton:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 A.T.L.A.S</h1>
            <p>Next-Generation Profit Optimization System</p>
            <p><strong>Advanced Trading & Learning Analysis System</strong></p>
            <div style="margin-top: 20px; padding: 15px; background: rgba(0,255,136,0.1); border-radius: 10px; border: 1px solid rgba(0,255,136,0.3);">
                <strong>🎯 Status: FULLY OPERATIONAL</strong><br>
                📚 Educational Paper Trading Mode | 🤖 AI-Enhanced Intelligence | 🔄 Profit Optimization Active
            </div>
        </div>

        <div class="status-grid">
            <div class="status-card">
                <h3>🎯 Profit Optimization Features</h3>
                <ul>
                    <li>Enhanced TTM Squeeze Analysis</li>
                    <li>Multi-Strategy Alpha Generation</li>
                    <li>Intelligent Trade Recycling</li>
                    <li>Dynamic Risk Management</li>
                    <li>Real-time RL Optimization</li>
                </ul>
            </div>
            <div class="status-card">
                <h3>🤖 AI Integration Status</h3>
                <ul>
                    <li>OpenAI GPT-4 Active</li>
                    <li>Predicto API Integrated</li>
                    <li>Multi-Agent Coordination</li>
                    <li>Chain-of-Thought Analysis</li>
                    <li>Educational Insights</li>
                </ul>
            </div>
            <div class="status-card">
                <h3>📊 Market Data Sources</h3>
                <ul>
                    <li>Alpaca Markets API</li>
                    <li>Financial Modeling Prep</li>
                    <li>Predicto Deep Learning</li>
                    <li>Real-time Quote Data</li>
                    <li>Historical Analysis</li>
                </ul>
            </div>
        </div>

        <div class="api-section">
            <h2>🚀 API Endpoints</h2>
            <p>Access A.T.L.A.S powerful trading intelligence through these endpoints:</p>
            <div class="api-buttons">
                <a href="/docs" class="api-button">📖 API Documentation</a>
                <a href="/api/v1/health" class="api-button">🔍 Health Check</a>
                <button onclick="testProfitOptimization()" class="api-button profit-optimizer">🎯 Test Profit Optimization</button>
                <a href="/api/v1/cot/dashboard" class="api-button">📊 CoT Dashboard</a>
            </div>
        </div>

        <div class="chat-section">
            <h3>💬 Chat with A.T.L.A.S AI Trading Assistant</h3>
            <div class="chat-container">
                <div id="chatMessages" class="chat-messages">
                    <div class="message assistant-message">
                        <div class="message-content">
                            <strong>🤖 A.T.L.A.S:</strong> Hello! I'm your AI trading assistant. I can help you with:
                            <ul>
                                <li>📈 Market analysis and trading strategies</li>
                                <li>💰 Profit optimization plans</li>
                                <li>🎓 Trading education and explanations</li>
                                <li>⚠️ Risk management advice</li>
                                <li>📊 Technical analysis insights</li>
                            </ul>
                            What would you like to explore today?
                        </div>
                    </div>
                </div>
                <div class="chat-input-container">
                    <input type="text" id="chatInput" placeholder="Ask me anything about trading... (e.g., 'Help me make $50 today')" />
                    <button onclick="sendMessage()" id="sendButton">
                        <span id="sendIcon">📤</span>
                        <span id="loadingIcon" style="display: none;">⏳</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Quick Test: Profit Optimization System</h3>
            <div class="test-form">
                <div class="form-group">
                    <label for="accountSize">Account Size ($)</label>
                    <input type="number" id="accountSize" value="100000" min="1000" step="1000">
                </div>
                <button onclick="testProfitOptimization()" class="api-button profit-optimizer">
                    🚀 Generate Profit Optimization Plan
                </button>
            </div>
            <div id="result"></div>
        </div>
    </div>

    <script>
        // Chat functionality
        async function sendMessage() {
            const chatInput = document.getElementById('chatInput');
            const message = chatInput.value.trim();

            if (!message) return;

            // Add user message to chat
            addMessageToChat('user', message);
            chatInput.value = '';

            // Show loading state
            setLoadingState(true);

            try {
                const response = await fetch('/api/v1/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        context: {
                            interface: 'atlas_html',
                            timestamp: new Date().toISOString()
                        }
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    // Add assistant response to chat
                    addMessageToChat('assistant', data.response || 'I received your message but had trouble generating a response.');

                    // If there's a trading plan, show it
                    if (data.trading_plan) {
                        addMessageToChat('assistant', `📋 **Trading Plan Generated:**\n${JSON.stringify(data.trading_plan, null, 2)}`);
                    }
                } else {
                    addMessageToChat('assistant', `❌ Sorry, I encountered an error: ${data.detail || 'Unknown error'}`);
                }
            } catch (error) {
                addMessageToChat('assistant', `❌ Network error: ${error.message}`);
            } finally {
                setLoadingState(false);
            }
        }

        function addMessageToChat(type, content) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            if (type === 'user') {
                messageContent.innerHTML = `<strong>👤 You:</strong> ${content}`;
            } else {
                messageContent.innerHTML = `<strong>🤖 A.T.L.A.S:</strong> ${content.replace(/\n/g, '<br>')}`;
            }

            messageDiv.appendChild(messageContent);
            chatMessages.appendChild(messageDiv);

            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function setLoadingState(isLoading) {
            const sendButton = document.getElementById('sendButton');
            const sendIcon = document.getElementById('sendIcon');
            const loadingIcon = document.getElementById('loadingIcon');
            const chatInput = document.getElementById('chatInput');

            sendButton.disabled = isLoading;
            chatInput.disabled = isLoading;

            if (isLoading) {
                sendIcon.style.display = 'none';
                loadingIcon.style.display = 'inline';
                addMessageToChat('assistant', '🤔 Thinking...');
            } else {
                sendIcon.style.display = 'inline';
                loadingIcon.style.display = 'none';
                // Remove the "Thinking..." message
                const messages = document.querySelectorAll('.message');
                const lastMessage = messages[messages.length - 1];
                if (lastMessage && lastMessage.textContent.includes('🤔 Thinking...')) {
                    lastMessage.remove();
                }
            }
        }

        // Handle Enter key in chat input
        document.addEventListener('DOMContentLoaded', function() {
            const chatInput = document.getElementById('chatInput');
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        });

        // Profit optimization test function
        async function testProfitOptimization() {
            const accountSize = document.getElementById('accountSize').value;
            const resultDiv = document.getElementById('result');

            resultDiv.innerHTML = '🔄 Generating profit optimization plan...';

            try {
                const response = await fetch('/api/v1/profit-optimization', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        account_size: parseFloat(accountSize),
                        active_positions: [],
                        closed_positions: []
                    })
                });

                const data = await response.json();

                if (data.success) {
                    const allocation = data.optimized_allocation || {};
                    const riskMetrics = data.risk_metrics || {};

                    resultDiv.innerHTML = `✅ SUCCESS! Profit Optimization Plan Generated

🎯 PLAN SUMMARY:
• Account Size: $${parseFloat(accountSize).toLocaleString()}
• Total Allocation: $${(allocation.total_allocation || 0).toLocaleString()}
• Allocation Percentage: ${(allocation.allocation_percentage || 0).toFixed(1)}%
• Diversification: ${allocation.diversification_count || 0} positions

📊 OPPORTUNITIES FOUND:
• Profit Opportunities: ${(data.profit_opportunities || []).length}
• Alpha Signals: ${(data.alpha_signals || []).length}
• Recycling Opportunities: ${(data.recycling_opportunities || []).length}

🛡️ RISK METRICS:
• Max Portfolio Risk: $${(riskMetrics.max_portfolio_risk || 0).toLocaleString()}
• Diversification Score: ${(riskMetrics.diversification_score || 0).toFixed(2)}
• Expected Return: ${((riskMetrics.expected_portfolio_return || 0) * 100).toFixed(1)}%

🎓 EDUCATIONAL INSIGHTS:
${(data.educational_insights || []).map((insight, i) => `${i + 1}. ${insight}`).join('\n')}

🎉 A.T.L.A.S Next-Generation Profit Optimization System is working perfectly!`;
                } else {
                    resultDiv.innerHTML = `❌ Error: ${data.error || 'Unknown error occurred'}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ Network Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
