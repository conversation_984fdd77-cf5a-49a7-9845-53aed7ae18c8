"""
A.T.L.A.S Reinforcement Learning Execution Engine
Institutional-grade execution algorithms with smart order routing
Based on "Reinforcement Learning for Algorithmic Trading" (arXiv:2311.13743)
"""

import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import asyncio

from .config import settings


class OrderType(Enum):
    """Order types for execution"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    TWAP = "twap"  # Time-Weighted Average Price
    VWAP = "vwap"  # Volume-Weighted Average Price


class ExecutionStrategy(Enum):
    """Execution strategy types"""
    AGGRESSIVE = "aggressive"  # Fast execution, higher market impact
    PASSIVE = "passive"       # Slow execution, lower market impact
    ADAPTIVE = "adaptive"     # RL-based adaptive execution
    STEALTH = "stealth"       # Minimize market impact


@dataclass
class ExecutionState:
    """Current state for RL execution decisions"""
    symbol: str
    remaining_quantity: int
    time_remaining_minutes: int
    current_spread: float
    volume_ratio: float  # Current volume vs average
    price_momentum: float
    market_impact_estimate: float
    execution_progress: float  # 0.0 to 1.0


@dataclass
class ExecutionAction:
    """Action taken by RL agent"""
    order_type: OrderType
    quantity: int
    price_offset: float  # Offset from mid-price
    urgency_factor: float  # 0.0 (patient) to 1.0 (urgent)


@dataclass
class ExecutionResult:
    """Result of execution action"""
    filled_quantity: int
    avg_fill_price: float
    market_impact: float
    slippage: float
    execution_cost: float
    time_taken_seconds: int


class RLExecutionAgent:
    """
    Enhanced Reinforcement Learning agent with intraday adaptation for profit optimization
    Learns from market conditions and execution outcomes with real-time adaptation
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # RL parameters (simplified Q-learning approach)
        self.learning_rate = 0.1
        self.discount_factor = 0.95
        self.epsilon = 0.1  # Exploration rate

        # State-action value table (Q-table)
        self.q_table: Dict[str, Dict[str, float]] = {}

        # Execution history for learning
        self.execution_history: List[Dict[str, Any]] = []

        # Enhanced performance metrics for profit optimization
        self.performance_metrics = {
            "total_executions": 0,
            "avg_slippage": 0.0,
            "avg_market_impact": 0.0,
            "success_rate": 0.0,
            "profit_factor": 1.0,
            "win_rate": 0.0,
            "avg_profit_per_trade": 0.0
        }

        # Intraday adaptation parameters
        self.adaptive_learning_rate = self.learning_rate
        self.market_regime = "normal"  # normal, volatile, trending
        self.recent_performance_window = 20  # Last 20 trades for adaptation
        self.performance_threshold = 0.65  # Minimum win rate threshold

        # Dynamic risk management
        self.base_stop_loss = 0.03  # 3% base stop loss
        self.dynamic_stop_multiplier = 1.0
        self.position_scale_factor = 1.0
        self.early_exit_threshold = 0.5  # Exit early if confidence drops below 50%
    
    def _state_to_key(self, state: ExecutionState) -> str:
        """Convert execution state to discrete key for Q-table"""
        
        # Discretize continuous variables
        spread_bucket = min(int(state.current_spread * 1000), 10)  # 0-10
        volume_bucket = min(int(state.volume_ratio * 2), 5)        # 0-5
        momentum_bucket = int(np.sign(state.price_momentum)) + 1   # 0,1,2
        progress_bucket = min(int(state.execution_progress * 4), 3) # 0-3
        
        return f"{spread_bucket}_{volume_bucket}_{momentum_bucket}_{progress_bucket}"
    
    def _action_to_key(self, action: ExecutionAction) -> str:
        """Convert execution action to discrete key"""
        
        order_type = action.order_type.value
        urgency = min(int(action.urgency_factor * 3), 2)  # 0,1,2
        
        return f"{order_type}_{urgency}"
    
    def select_action(self, state: ExecutionState) -> ExecutionAction:
        """Select optimal execution action using RL policy"""
        
        state_key = self._state_to_key(state)
        
        # Initialize Q-values for new states
        if state_key not in self.q_table:
            self.q_table[state_key] = {}
        
        # Available actions
        available_actions = self._get_available_actions(state)
        
        # Epsilon-greedy action selection
        if np.random.random() < self.epsilon:
            # Exploration: random action
            action = np.random.choice(available_actions)
        else:
            # Exploitation: best known action
            action = self._get_best_action(state_key, available_actions)
        
        return action
    
    def _get_available_actions(self, state: ExecutionState) -> List[ExecutionAction]:
        """Get available actions based on current state"""
        
        actions = []
        
        # Market order actions
        for urgency in [0.3, 0.6, 1.0]:
            actions.append(ExecutionAction(
                order_type=OrderType.MARKET,
                quantity=min(state.remaining_quantity, int(state.remaining_quantity * urgency)),
                price_offset=0.0,
                urgency_factor=urgency
            ))
        
        # Limit order actions
        for price_offset in [-0.001, 0.0, 0.001]:  # -0.1%, 0%, +0.1%
            for urgency in [0.2, 0.5, 0.8]:
                actions.append(ExecutionAction(
                    order_type=OrderType.LIMIT,
                    quantity=min(state.remaining_quantity, int(state.remaining_quantity * urgency)),
                    price_offset=price_offset,
                    urgency_factor=urgency
                ))
        
        return actions
    
    def _get_best_action(self, state_key: str, available_actions: List[ExecutionAction]) -> ExecutionAction:
        """Get action with highest Q-value"""
        
        best_action = available_actions[0]
        best_q_value = float('-inf')
        
        for action in available_actions:
            action_key = self._action_to_key(action)
            q_value = self.q_table[state_key].get(action_key, 0.0)
            
            if q_value > best_q_value:
                best_q_value = q_value
                best_action = action
        
        return best_action
    
    def update_q_value(self, state: ExecutionState, action: ExecutionAction, 
                      reward: float, next_state: Optional[ExecutionState] = None):
        """Update Q-value based on execution result"""
        
        state_key = self._state_to_key(state)
        action_key = self._action_to_key(action)
        
        # Initialize if needed
        if state_key not in self.q_table:
            self.q_table[state_key] = {}
        if action_key not in self.q_table[state_key]:
            self.q_table[state_key][action_key] = 0.0
        
        # Q-learning update
        current_q = self.q_table[state_key][action_key]
        
        if next_state is not None:
            next_state_key = self._state_to_key(next_state)
            if next_state_key in self.q_table:
                max_next_q = max(self.q_table[next_state_key].values()) if self.q_table[next_state_key] else 0.0
            else:
                max_next_q = 0.0
            
            # Q(s,a) = Q(s,a) + α[r + γ*max(Q(s',a')) - Q(s,a)]
            new_q = current_q + self.learning_rate * (reward + self.discount_factor * max_next_q - current_q)
        else:
            # Terminal state
            new_q = current_q + self.learning_rate * (reward - current_q)
        
        self.q_table[state_key][action_key] = new_q
    
    def calculate_reward(self, execution_result: ExecutionResult, target_metrics: Dict[str, float]) -> float:
        """Enhanced reward calculation for profit optimization"""

        # Base reward components
        slippage_penalty = -abs(execution_result.slippage) * 100  # Penalize slippage
        impact_penalty = -execution_result.market_impact * 50     # Penalize market impact
        speed_bonus = max(0, 10 - execution_result.time_taken_seconds / 60)  # Bonus for speed

        # Fill rate bonus
        fill_rate = execution_result.filled_quantity / max(1, execution_result.filled_quantity)
        fill_bonus = fill_rate * 20

        # Profit optimization bonuses
        profit_bonus = self._calculate_profit_bonus(execution_result, target_metrics)
        risk_management_bonus = self._calculate_risk_management_bonus(execution_result)

        total_reward = (slippage_penalty + impact_penalty + speed_bonus +
                       fill_bonus + profit_bonus + risk_management_bonus)

        return total_reward

    def _calculate_profit_bonus(self, execution_result: ExecutionResult,
                               target_metrics: Dict[str, float]) -> float:
        """Calculate profit optimization bonus"""
        try:
            # Bonus for achieving better than expected execution price
            expected_price = target_metrics.get("expected_price", execution_result.execution_price)
            price_improvement = abs(execution_result.execution_price - expected_price) / expected_price

            # Bonus for maintaining low execution costs
            cost_efficiency = max(0, 1 - execution_result.execution_cost / (execution_result.filled_quantity * execution_result.execution_price))

            return (price_improvement * 50) + (cost_efficiency * 30)

        except Exception as e:
            return 0.0

    def _calculate_risk_management_bonus(self, execution_result: ExecutionResult) -> float:
        """Calculate risk management bonus"""
        try:
            # Bonus for maintaining low market impact
            impact_bonus = max(0, 20 - execution_result.market_impact * 100)

            # Bonus for consistent execution quality
            consistency_bonus = 10 if execution_result.slippage < 0.001 else 0

            return impact_bonus + consistency_bonus

        except Exception as e:
            return 0.0

    def adapt_intraday_parameters(self) -> None:
        """Adapt RL parameters based on recent performance"""
        try:
            if len(self.execution_history) < self.recent_performance_window:
                return

            # Analyze recent performance
            recent_trades = self.execution_history[-self.recent_performance_window:]
            recent_win_rate = sum(1 for trade in recent_trades if trade.get("profit", 0) > 0) / len(recent_trades)
            recent_avg_profit = np.mean([trade.get("profit", 0) for trade in recent_trades])

            # Adapt learning rate based on performance
            if recent_win_rate < self.performance_threshold:
                # Poor performance - increase exploration and learning rate
                self.adaptive_learning_rate = min(0.3, self.learning_rate * 1.5)
                self.epsilon = min(0.3, self.epsilon * 1.2)
                self.logger.warning(f"Poor performance detected. Increasing exploration: ε={self.epsilon:.3f}")
            else:
                # Good performance - reduce exploration, maintain learning
                self.adaptive_learning_rate = max(0.05, self.learning_rate * 0.9)
                self.epsilon = max(0.05, self.epsilon * 0.9)

            # Adapt position sizing based on recent profitability
            if recent_avg_profit > 0.02:  # 2% average profit
                self.position_scale_factor = min(1.5, self.position_scale_factor * 1.1)
            elif recent_avg_profit < -0.01:  # -1% average loss
                self.position_scale_factor = max(0.5, self.position_scale_factor * 0.9)

            # Adapt stop-loss based on market volatility
            recent_volatility = np.std([trade.get("price_change", 0) for trade in recent_trades])
            if recent_volatility > 0.02:  # High volatility
                self.dynamic_stop_multiplier = min(1.5, 1.0 + recent_volatility * 10)
            else:
                self.dynamic_stop_multiplier = max(0.8, 1.0 + recent_volatility * 5)

            self.logger.info(f"Intraday adaptation: Win rate={recent_win_rate:.2f}, "
                           f"Scale factor={self.position_scale_factor:.2f}, "
                           f"Stop multiplier={self.dynamic_stop_multiplier:.2f}")

        except Exception as e:
            self.logger.error(f"Error in intraday adaptation: {e}")

    def should_exit_early(self, current_profit: float, confidence: float) -> bool:
        """Determine if position should be exited early based on conditions"""
        try:
            # Exit early if confidence drops significantly
            if confidence < self.early_exit_threshold:
                return True

            # Exit early if profit target is reached
            if current_profit > 0.05:  # 5% profit
                return True

            # Exit early if loss exceeds dynamic stop
            dynamic_stop = self.base_stop_loss * self.dynamic_stop_multiplier
            if current_profit < -dynamic_stop:
                return True

            return False

        except Exception as e:
            self.logger.error(f"Error in early exit decision: {e}")
            return False

    def calculate_dynamic_position_size(self, base_size: int, confidence: float) -> int:
        """Calculate dynamic position size based on confidence and recent performance"""
        try:
            # Scale based on confidence
            confidence_multiplier = 0.5 + (confidence * 1.0)  # 0.5x to 1.5x based on confidence

            # Apply recent performance scaling
            performance_multiplier = self.position_scale_factor

            # Calculate final size
            dynamic_size = int(base_size * confidence_multiplier * performance_multiplier)

            # Apply safety limits
            max_size = base_size * 2  # Never more than 2x base size
            min_size = max(1, base_size // 2)  # Never less than half base size

            return max(min_size, min(dynamic_size, max_size))

        except Exception as e:
            self.logger.error(f"Error calculating dynamic position size: {e}")
            return base_size

    def record_execution(self, state: ExecutionState, action: ExecutionAction,
                        result: ExecutionResult):
        """Record execution for learning and performance tracking"""
        
        execution_record = {
            "timestamp": datetime.now(),
            "state": state.__dict__,
            "action": action.__dict__,
            "result": result.__dict__,
            "reward": self.calculate_reward(result, {})
        }
        
        self.execution_history.append(execution_record)
        
        # Update performance metrics
        self._update_performance_metrics(result)
        
        # Learn from execution
        reward = execution_record["reward"]
        self.update_q_value(state, action, reward)
    
    def _update_performance_metrics(self, result: ExecutionResult):
        """Update running performance metrics"""
        
        self.performance_metrics["total_executions"] += 1
        n = self.performance_metrics["total_executions"]
        
        # Running averages
        self.performance_metrics["avg_slippage"] = (
            (self.performance_metrics["avg_slippage"] * (n-1) + abs(result.slippage)) / n
        )
        
        self.performance_metrics["avg_market_impact"] = (
            (self.performance_metrics["avg_market_impact"] * (n-1) + result.market_impact) / n
        )
        
        # Success rate (filled > 90% of intended quantity)
        success = 1.0 if result.filled_quantity > 0 else 0.0
        self.performance_metrics["success_rate"] = (
            (self.performance_metrics["success_rate"] * (n-1) + success) / n
        )


class SmartOrderRouter:
    """
    Smart order routing system with RL-based execution optimization
    Routes orders to optimal venues and execution strategies
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.rl_agent = RLExecutionAgent()
        
        # Venue characteristics (simplified)
        self.venues = {
            "primary": {"latency": 1.0, "liquidity": 1.0, "cost": 0.001},
            "dark_pool": {"latency": 2.0, "liquidity": 0.7, "cost": 0.0005},
            "ecn": {"latency": 0.5, "liquidity": 0.8, "cost": 0.0008}
        }
    
    async def execute_order(self, symbol: str, quantity: int, side: str, 
                           strategy: ExecutionStrategy = ExecutionStrategy.ADAPTIVE,
                           max_time_minutes: int = 30) -> Dict[str, Any]:
        """Execute order using smart routing and RL optimization"""
        
        try:
            # Initialize execution state
            state = ExecutionState(
                symbol=symbol,
                remaining_quantity=quantity,
                time_remaining_minutes=max_time_minutes,
                current_spread=0.01,  # Would get from market data
                volume_ratio=1.0,     # Would calculate from real data
                price_momentum=0.0,   # Would calculate from price data
                market_impact_estimate=0.001,
                execution_progress=0.0
            )
            
            total_filled = 0
            total_cost = 0.0
            execution_steps = []
            
            start_time = datetime.now()
            
            while state.remaining_quantity > 0 and state.time_remaining_minutes > 0:
                # Get RL action
                action = self.rl_agent.select_action(state)
                
                # Execute action (simulated)
                result = await self._simulate_execution(state, action)
                
                # Update totals
                total_filled += result.filled_quantity
                total_cost += result.execution_cost
                
                # Record for learning
                self.rl_agent.record_execution(state, action, result)
                
                execution_steps.append({
                    "action": action.__dict__,
                    "result": result.__dict__,
                    "timestamp": datetime.now()
                })
                
                # Update state
                state.remaining_quantity -= result.filled_quantity
                state.execution_progress = total_filled / quantity
                state.time_remaining_minutes -= result.time_taken_seconds / 60
                
                # Break if fully filled
                if state.remaining_quantity <= 0:
                    break
                
                # Small delay between executions
                await asyncio.sleep(0.1)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return {
                "success": True,
                "symbol": symbol,
                "total_filled": total_filled,
                "fill_rate": total_filled / quantity,
                "total_cost": total_cost,
                "avg_price": total_cost / max(total_filled, 1),
                "execution_time_seconds": execution_time,
                "execution_steps": execution_steps,
                "rl_performance": self.rl_agent.performance_metrics
            }
            
        except Exception as e:
            self.logger.error(f"Error in smart order execution: {e}")
            return {
                "success": False,
                "error": str(e),
                "symbol": symbol,
                "total_filled": 0
            }
    
    async def _simulate_execution(self, state: ExecutionState, action: ExecutionAction) -> ExecutionResult:
        """Simulate order execution (would interface with real brokers)"""
        
        # Simulate execution based on action type and market conditions
        if action.order_type == OrderType.MARKET:
            # Market orders fill quickly but with more slippage
            filled_quantity = min(action.quantity, state.remaining_quantity)
            slippage = 0.001 * action.urgency_factor  # Higher urgency = more slippage
            execution_time = 1  # 1 second
            
        elif action.order_type == OrderType.LIMIT:
            # Limit orders may partially fill
            fill_probability = 0.8 - abs(action.price_offset) * 100  # Better price = lower fill rate
            filled_quantity = int(action.quantity * max(0.1, fill_probability))
            slippage = action.price_offset
            execution_time = 5  # 5 seconds
            
        else:
            # Default execution
            filled_quantity = min(action.quantity, state.remaining_quantity)
            slippage = 0.0005
            execution_time = 2
        
        # Calculate market impact
        market_impact = min(0.01, filled_quantity / 10000 * 0.001)  # Impact based on size
        
        # Calculate execution cost
        execution_cost = filled_quantity * (100 + slippage * 100)  # Simplified cost
        
        return ExecutionResult(
            filled_quantity=filled_quantity,
            avg_fill_price=100 + slippage * 100,  # Simplified price
            market_impact=market_impact,
            slippage=slippage,
            execution_cost=execution_cost,
            time_taken_seconds=execution_time
        )
    
    def get_execution_analytics(self) -> Dict[str, Any]:
        """Get execution performance analytics"""
        
        return {
            "rl_performance": self.rl_agent.performance_metrics,
            "total_executions": len(self.rl_agent.execution_history),
            "q_table_size": len(self.rl_agent.q_table),
            "learning_progress": {
                "exploration_rate": self.rl_agent.epsilon,
                "learning_rate": self.rl_agent.learning_rate
            }
        }
