"""
A.T.L.A.S Enhanced Memory System
Session-based contextual memory for goals, capital, trade history, and agent coordination
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path

@dataclass
class TradingGoal:
    """Represents a user's trading goal"""
    goal_id: str
    description: str
    target_amount: float
    timeframe: str
    created_at: datetime
    status: str = "active"  # active, completed, cancelled
    progress: float = 0.0
    trades_executed: List[str] = None
    
    def __post_init__(self):
        if self.trades_executed is None:
            self.trades_executed = []

@dataclass
class TradingSession:
    """Represents a trading session"""
    session_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    goals: List[TradingGoal] = None
    pnl: float = 0.0
    trades_count: int = 0
    emotional_state_history: List[Dict] = None
    communication_mode: str = "mentor"
    
    def __post_init__(self):
        if self.goals is None:
            self.goals = []
        if self.emotional_state_history is None:
            self.emotional_state_history = []

@dataclass
class UserProfile:
    """Enhanced user profile with persistent memory"""
    user_id: str
    account_size: float
    risk_tolerance: str
    experience_level: str
    communication_preferences: Dict[str, Any]
    trading_history: List[Dict] = None
    session_history: List[TradingSession] = None
    learned_patterns: Dict[str, Any] = None
    created_at: datetime = None
    last_updated: datetime = None
    
    def __post_init__(self):
        if self.trading_history is None:
            self.trading_history = []
        if self.session_history is None:
            self.session_history = []
        if self.learned_patterns is None:
            self.learned_patterns = {}
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.last_updated is None:
            self.last_updated = datetime.utcnow()

class EnhancedMemorySystem:
    """
    Enhanced memory system for A.T.L.A.S with session-based contextual memory
    """
    
    def __init__(self, memory_dir: str = "memory"):
        self.logger = logging.getLogger(__name__)
        self.memory_dir = Path(memory_dir)
        self.memory_dir.mkdir(exist_ok=True)
        
        # Current session data
        self.current_session: Optional[TradingSession] = None
        self.current_user_profile: Optional[UserProfile] = None
        
        # Agent memory for multi-agent coordination
        self.agent_memory = {
            "technical_agent": {"last_analysis": None, "confidence_history": []},
            "risk_agent": {"risk_assessments": [], "warnings_issued": []},
            "sentiment_agent": {"sentiment_history": [], "market_regime": None},
            "execution_agent": {"trade_history": [], "performance_metrics": {}}
        }
        
        # Conversation context
        self.conversation_context = {
            "recent_topics": [],
            "user_questions": [],
            "system_responses": [],
            "context_window": 10  # Keep last 10 exchanges
        }

    def start_session(self, user_id: str = "default_user") -> str:
        """Start a new trading session"""
        session_id = f"session_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        
        # Load or create user profile
        self.current_user_profile = self.load_user_profile(user_id)
        
        # Create new session
        self.current_session = TradingSession(
            session_id=session_id,
            start_time=datetime.utcnow(),
            communication_mode=self.current_user_profile.communication_preferences.get("default_mode", "mentor")
        )
        
        self.logger.info(f"Started trading session: {session_id}")
        return session_id

    def end_session(self):
        """End the current trading session and save data"""
        if self.current_session:
            self.current_session.end_time = datetime.utcnow()
            
            # Add session to user profile
            if self.current_user_profile:
                self.current_user_profile.session_history.append(self.current_session)
                self.save_user_profile(self.current_user_profile)
            
            # Save session data
            self.save_session_data(self.current_session)
            
            self.logger.info(f"Ended trading session: {self.current_session.session_id}")
            self.current_session = None

    def add_trading_goal(self, description: str, target_amount: float, timeframe: str) -> str:
        """Add a new trading goal to the current session"""
        if not self.current_session:
            raise ValueError("No active session")
        
        goal_id = f"goal_{len(self.current_session.goals) + 1}"
        goal = TradingGoal(
            goal_id=goal_id,
            description=description,
            target_amount=target_amount,
            timeframe=timeframe,
            created_at=datetime.utcnow()
        )
        
        self.current_session.goals.append(goal)
        self.logger.info(f"Added trading goal: {description} (${target_amount})")
        return goal_id

    def update_goal_progress(self, goal_id: str, progress: float, trade_id: Optional[str] = None):
        """Update progress on a trading goal"""
        if not self.current_session:
            return
        
        for goal in self.current_session.goals:
            if goal.goal_id == goal_id:
                goal.progress = progress
                if trade_id:
                    goal.trades_executed.append(trade_id)
                
                if progress >= 100.0:
                    goal.status = "completed"
                
                self.logger.info(f"Updated goal {goal_id} progress: {progress}%")
                break

    def record_emotional_state(self, emotional_state: str, context: str = ""):
        """Record user's emotional state for session tracking"""
        if not self.current_session:
            return
        
        emotion_record = {
            "timestamp": datetime.utcnow().isoformat(),
            "emotional_state": emotional_state,
            "context": context
        }
        
        self.current_session.emotional_state_history.append(emotion_record)
        
        # Keep only last 20 emotional state records
        if len(self.current_session.emotional_state_history) > 20:
            self.current_session.emotional_state_history = self.current_session.emotional_state_history[-20:]

    def update_agent_memory(self, agent_name: str, data: Dict[str, Any]):
        """Update memory for a specific agent"""
        if agent_name in self.agent_memory:
            self.agent_memory[agent_name].update(data)
            self.agent_memory[agent_name]["last_updated"] = datetime.utcnow().isoformat()

    def get_agent_memory(self, agent_name: str) -> Dict[str, Any]:
        """Get memory for a specific agent"""
        return self.agent_memory.get(agent_name, {})

    def add_conversation_context(self, user_message: str, system_response: str, topic: str = ""):
        """Add conversation context for better continuity"""
        self.conversation_context["user_questions"].append({
            "timestamp": datetime.utcnow().isoformat(),
            "message": user_message,
            "topic": topic
        })
        
        self.conversation_context["system_responses"].append({
            "timestamp": datetime.utcnow().isoformat(),
            "response": system_response,
            "topic": topic
        })
        
        if topic:
            self.conversation_context["recent_topics"].append(topic)
        
        # Maintain context window
        window = self.conversation_context["context_window"]
        for key in ["user_questions", "system_responses"]:
            if len(self.conversation_context[key]) > window:
                self.conversation_context[key] = self.conversation_context[key][-window:]
        
        if len(self.conversation_context["recent_topics"]) > window:
            self.conversation_context["recent_topics"] = self.conversation_context["recent_topics"][-window:]

    def get_session_summary(self) -> Dict[str, Any]:
        """Get summary of current session"""
        if not self.current_session:
            return {"error": "No active session"}
        
        return {
            "session_id": self.current_session.session_id,
            "duration": str(datetime.utcnow() - self.current_session.start_time),
            "goals": [asdict(goal) for goal in self.current_session.goals],
            "pnl": self.current_session.pnl,
            "trades_count": self.current_session.trades_count,
            "emotional_journey": self.current_session.emotional_state_history[-5:],  # Last 5 states
            "communication_mode": self.current_session.communication_mode
        }

    def load_user_profile(self, user_id: str) -> UserProfile:
        """Load user profile from storage"""
        profile_file = self.memory_dir / f"profile_{user_id}.json"
        
        if profile_file.exists():
            try:
                with open(profile_file, 'r') as f:
                    data = json.load(f)
                
                # Convert datetime strings back to datetime objects
                data['created_at'] = datetime.fromisoformat(data['created_at'])
                data['last_updated'] = datetime.fromisoformat(data['last_updated'])
                
                return UserProfile(**data)
            except Exception as e:
                self.logger.error(f"Error loading user profile: {e}")
        
        # Create default profile
        return UserProfile(
            user_id=user_id,
            account_size=25000.0,
            risk_tolerance="moderate",
            experience_level="beginner",
            communication_preferences={
                "default_mode": "mentor",
                "explanation_depth": "detailed",
                "analogy_preference": True
            }
        )

    def save_user_profile(self, profile: UserProfile):
        """Save user profile to storage"""
        profile.last_updated = datetime.utcnow()
        profile_file = self.memory_dir / f"profile_{profile.user_id}.json"
        
        try:
            # Convert to dict and handle datetime serialization
            data = asdict(profile)
            data['created_at'] = data['created_at'].isoformat()
            data['last_updated'] = data['last_updated'].isoformat()
            
            with open(profile_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
                
            self.logger.info(f"Saved user profile: {profile.user_id}")
        except Exception as e:
            self.logger.error(f"Error saving user profile: {e}")

    def save_session_data(self, session: TradingSession):
        """Save session data to storage"""
        session_file = self.memory_dir / f"{session.session_id}.json"
        
        try:
            data = asdict(session)
            # Handle datetime serialization
            data['start_time'] = data['start_time'].isoformat()
            if data['end_time']:
                data['end_time'] = data['end_time'].isoformat()
            
            with open(session_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
                
            self.logger.info(f"Saved session data: {session.session_id}")
        except Exception as e:
            self.logger.error(f"Error saving session data: {e}")
