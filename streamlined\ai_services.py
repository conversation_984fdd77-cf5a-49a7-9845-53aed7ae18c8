"""
Streamlined A.T.L.A.S Trading System - AI Services
Consolidated LLM, sentiment analysis, and event explanation services
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logging.warning("OpenAI not available. Install with: pip install openai")

from config import settings, AI_PROMPTS
from models import (
    Quote, NewsArticle, SentimentAnalysis, MarketEvent, EventExplanation,
    TechnicalIndicators, AIResponse, ChatMessage
)
from market_data import MarketDataService
from trading_books_rag import TradingEducationRAG
from trading_rules import TradingRulesEngine, RuleValidationResult
from feedback_system import FeedbackSystem
from validation_engine import TradingValidationEngine, ValidationResult


class AIServices:
    """Enhanced AI services with personalization, validation, and feedback learning"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.openai_client = None
        self.market_data = MarketDataService()
        self.education_rag = TradingEducationRAG()
        self.trading_rules = TradingRulesEngine()
        self.feedback_system = FeedbackSystem()
        self.validation_engine = TradingValidationEngine()
        self.conversation_history: List[ChatMessage] = []
        self.session_id = self._generate_session_id()

        # Mentor mode configuration for enhanced conversational intelligence
        self.mentor_config = {
            "beginner_mode": True,
            "explanation_depth": "detailed",
            "analogy_preference": True,
            "encouragement_level": "high",
            "realism_check": True,
            "user_experience_level": "beginner"  # auto-detected
        }

        # Market analogies database for relatable explanations
        self.market_analogies = {
            "flat_market": "trying to squeeze juice from a rock",
            "volatile_market": "riding a roller coaster in the dark",
            "bull_market": "swimming with the current",
            "bear_market": "walking uphill in a snowstorm",
            "breakout": "a dam bursting after heavy rain",
            "support_level": "a safety net under a tightrope walker",
            "resistance_level": "a ceiling that keeps getting hit",
            "volume_spike": "a crowd suddenly rushing through a door",
            "low_volume": "a ghost town on a Sunday morning",
            "risk_management": "wearing a seatbelt while driving",
            "diversification": "not putting all your eggs in one basket",
            "stop_loss": "an emergency brake that activates automatically",
            "position_sizing": "deciding how much gas to put in your tank",
            "market_timing": "trying to catch a falling knife"
        }

        # Mentor response templates
        self.mentor_templates = {
            "unrealistic_request": "I understand you're looking for {goal}, but {analogy}. Let me show you a safer approach that could work over {timeframe} instead...",
            "educational_intro": "Great question! Think of {concept} like {analogy}. Here's how it works in simple terms:",
            "risk_warning": "Hold on! This reminds me of {analogy}. Let's make sure we're being safe here:",
            "encouragement": "You're asking great questions! {concept} is exactly the kind of thing successful traders think about.",
            "reality_check": "I love your enthusiasm, but {situation} is like {analogy}. Here's what's actually possible:"
        }

        # Initialize OpenAI
        if OPENAI_AVAILABLE and settings.OPENAI_API_KEY:
            try:
                self.openai_client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)
                self.logger.info("OpenAI client initialized")
            except Exception as e:
                self.logger.error(f"Error initializing OpenAI: {e}")
        else:
            self.logger.warning("OpenAI not available - using fallback responses")

    def _generate_session_id(self) -> str:
        """Generate unique session ID"""
        import uuid
        return str(uuid.uuid4())[:8]

    def _detect_user_experience_level(self, message: str) -> str:
        """Detect user experience level from their message patterns"""
        message_lower = message.lower()

        # Beginner indicators
        beginner_patterns = [
            "what is", "how do", "explain", "teach me", "i'm new", "beginner",
            "don't understand", "confused", "simple terms", "basic", "help me learn"
        ]

        # Advanced indicators
        advanced_patterns = [
            "gamma", "theta", "vega", "delta", "greeks", "volatility skew",
            "iron condor", "butterfly", "straddle", "strangle", "kelly criterion",
            "sharpe ratio", "sortino", "maximum drawdown", "var", "cvar"
        ]

        # Intermediate indicators
        intermediate_patterns = [
            "support", "resistance", "moving average", "rsi", "macd", "bollinger",
            "volume", "breakout", "trend", "chart pattern", "technical analysis"
        ]

        # Count matches
        beginner_score = sum(1 for pattern in beginner_patterns if pattern in message_lower)
        advanced_score = sum(1 for pattern in advanced_patterns if pattern in message_lower)
        intermediate_score = sum(1 for pattern in intermediate_patterns if pattern in message_lower)

        # Determine level
        if advanced_score > 0:
            return "advanced"
        elif intermediate_score > beginner_score:
            return "intermediate"
        else:
            return "beginner"

    def _apply_mentor_style(self, response: str, context: Dict[str, Any], intent: str) -> str:
        """Apply mentor-style communication to response"""

        # Detect user experience level
        user_level = self._detect_user_experience_level(context.get("original_message", ""))
        self.mentor_config["user_experience_level"] = user_level

        # Apply appropriate style based on user level and intent
        if user_level == "beginner" and self.mentor_config["analogy_preference"]:
            response = self._add_analogies_to_response(response, intent)

        if self.mentor_config["encouragement_level"] == "high":
            response = self._add_encouragement(response, user_level)

        if self.mentor_config["realism_check"] and intent in ["create_plan", "profit_target"]:
            response = self._add_reality_check(response, context)

        return response

    def _add_analogies_to_response(self, response: str, intent: str) -> str:
        """Add relevant market analogies to make concepts more relatable"""

        # Map intents to relevant analogies
        analogy_mappings = {
            "stock_analysis": ["support_level", "resistance_level", "breakout"],
            "risk_management": ["risk_management", "stop_loss", "diversification"],
            "market_explanation": ["bull_market", "bear_market", "volatile_market"],
            "education": ["position_sizing", "market_timing"]
        }

        relevant_analogies = analogy_mappings.get(intent, ["risk_management"])

        # Add analogy section if not already present
        if "analogy" not in response.lower() and "like" not in response.lower():
            analogy_key = relevant_analogies[0] if relevant_analogies else "risk_management"
            analogy = self.market_analogies.get(analogy_key, "wearing a seatbelt while driving")

            response += f"\n\n**💡 Think of it this way:** This is like {analogy} - it's all about being prepared and managing what you can control."

        return response

    def _add_encouragement(self, response: str, user_level: str) -> str:
        """Add appropriate encouragement based on user level"""

        encouragements = {
            "beginner": [
                "You're asking exactly the right questions!",
                "Great thinking - this is how successful traders approach the market!",
                "I love that you're being thoughtful about this!",
                "You're on the right track with this question!"
            ],
            "intermediate": [
                "Your understanding is developing nicely!",
                "That's a sophisticated way to think about it!",
                "You're connecting the dots well!",
                "Good insight - you're thinking like a pro!"
            ],
            "advanced": [
                "Excellent analysis approach!",
                "Your technical understanding is solid!",
                "That's exactly how institutional traders think!",
                "You're applying advanced concepts correctly!"
            ]
        }

        import random
        encouragement = random.choice(encouragements.get(user_level, encouragements["beginner"]))

        # Add encouragement if not already present
        if not any(phrase in response for phrase in ["great", "excellent", "good", "love", "right"]):
            response = f"**🎯 {encouragement}**\n\n{response}"

        return response

    def _add_reality_check(self, response: str, context: Dict[str, Any]) -> str:
        """Add gentle reality checks for unrealistic expectations with mentor-style guidance"""

        # Extract profit targets or expectations from context
        message = context.get("original_message", "").lower()

        # Check for unrealistic daily profit expectations
        import re
        profit_matches = re.findall(r'\$?(\d+)', message)

        if profit_matches and any(word in message for word in ["today", "daily", "quick", "fast", "week"]):
            try:
                target = float(profit_matches[0])
                account_size = context.get("account_size", 25000)
                daily_return_percent = (target / account_size) * 100

                if daily_return_percent > 5:  # More than 5% return
                    timeframe = "today" if any(word in message for word in ["today", "daily"]) else "this week"

                    reality_check = f"""
**🎯 I understand you're looking for ${target:.0f} {timeframe}!**

**💡 Reality Check:** Making ${target:.0f} {timeframe} would be a {daily_return_percent:.1f}% return. That's like trying to squeeze juice from a rock - possible, but not sustainable.

**📚 Professional Perspective:**
What you're asking for is what hedge funds dream of achieving monthly. Warren Buffett averages about 20% per YEAR.

**🎓 Here's what I can do instead:**
Let me show you a realistic approach that could work over a longer timeframe and actually build sustainable wealth.
                    """.strip()

                    response = reality_check + "\n\n" + response

                elif daily_return_percent > 2:  # 2-5% return - ambitious but possible
                    reality_check = f"""
**🎯 Great question about making ${target:.0f}!**

**💡 Think of it this way:** You're asking for a {daily_return_percent:.1f}% return, which is ambitious but not impossible with the right approach.

**🎓 Smart Approach:**
Instead of forcing quick gains, let me show you how to identify high-probability setups that could realistically achieve this over a few days with proper risk management.
                    """.strip()

                    response = reality_check + "\n\n" + response

            except (ValueError, ZeroDivisionError):
                pass

        return response
    
    async def process_chat_message(self, message: str, context: Optional[Dict[str, Any]] = None) -> AIResponse:
        """Enhanced chat processing with validation, rules, feedback learning, and mentor-style communication"""
        start_time = datetime.now()

        try:
            # Add user message to history
            user_msg = ChatMessage(role="user", content=message)
            self.conversation_history.append(user_msg)

            # Determine intent and gather enhanced market context
            intent = self._analyze_intent(message)
            market_context = await self._gather_enhanced_market_context(message, intent)

            # Add original message to context for mentor-style processing
            market_context["original_message"] = message
            market_context["user_experience_level"] = self._detect_user_experience_level(message)

            # Generate initial response based on intent
            if intent == "stock_analysis":
                response = await self._handle_enhanced_stock_analysis(message, market_context)
            elif intent == "education":
                response = await self._handle_education_query(message)
            elif intent == "market_explanation":
                response = await self._handle_market_explanation(message, market_context)
            elif intent == "technical_question":
                response = await self._handle_technical_question(message, market_context)
            else:
                response = await self._handle_general_chat(message, market_context)

            # Apply mentor-style enhancements to the response
            if response and response.response:
                response.response = self._apply_mentor_style(response.response, market_context, intent)

            # Validate response for trading accuracy
            validation_result, validation_reason, validation_details = await self.validation_engine.validate_ai_response(
                response.response, market_context
            )

            # Add validation info to response
            response.validation_passed = validation_result == ValidationResult.VALID
            response.validation_reason = validation_reason

            # If validation failed, provide corrected response
            if validation_result == ValidationResult.INVALID:
                response = await self._generate_corrected_response(message, market_context, validation_reason)

            # Store conversation with feedback system
            message_id = await self.feedback_system.store_conversation(
                self.session_id, user_msg, response, market_context
            )

            # Store response metrics
            response_time = (datetime.now() - start_time).total_seconds()
            await self.feedback_system.store_response_metrics(
                message_id, response_time, validation_passed=response.validation_passed
            )

            # Add assistant response to history
            assistant_msg = ChatMessage(
                role="assistant",
                content=response.response,
                function_result={"type": intent, "context": market_context, "validation": validation_details}
            )
            self.conversation_history.append(assistant_msg)

            return response

        except Exception as e:
            self.logger.error(f"Error processing chat message: {e}")
            return AIResponse(
                response="I encountered an error processing your message. Please try again.",
                type="error",
                validation_passed=False
            )
    
    def _analyze_intent(self, message: str) -> str:
        """Analyze user intent from message"""
        message_lower = message.lower()
        
        # Stock analysis keywords
        if any(word in message_lower for word in ["analyze", "analysis", "what's", "how's", "price", "stock"]):
            return "stock_analysis"
        
        # Education keywords
        elif any(word in message_lower for word in ["explain", "what is", "how to", "teach", "learn", "book", "concept"]):
            return "education"
        
        # Market explanation keywords
        elif any(word in message_lower for word in ["why", "what happened", "drop", "rise", "move", "news"]):
            return "market_explanation"
        
        # Technical question keywords
        elif any(word in message_lower for word in ["rsi", "macd", "indicator", "pattern", "support", "resistance"]):
            return "technical_question"
        
        else:
            return "general_chat"
    
    async def _gather_market_context(self, message: str, intent: str) -> Dict[str, Any]:
        """Gather relevant market context for the message"""
        context = {}
        
        # Extract symbol if mentioned
        symbol = self._extract_symbol(message)
        if symbol:
            try:
                async with self.market_data:
                    # Get quote and basic data
                    quote = await self.market_data.get_real_time_quote(symbol)
                    context["quote"] = quote.dict()
                    
                    # Get news for market explanation intent
                    if intent in ["market_explanation", "stock_analysis"]:
                        news = await self.market_data.get_market_news(symbol, limit=3)
                        context["news"] = [article.dict() for article in news]
                    
                    context["symbol"] = symbol
                    
            except Exception as e:
                self.logger.warning(f"Error gathering market context for {symbol}: {e}")
        
        return context
    
    def _extract_symbol(self, message: str) -> Optional[str]:
        """Extract stock symbol from message"""
        import re
        
        # Look for common stock symbols (2-5 uppercase letters)
        symbols = re.findall(r'\b[A-Z]{2,5}\b', message)
        
        # Filter out common words that might match pattern
        common_words = {"THE", "AND", "FOR", "ARE", "BUT", "NOT", "YOU", "ALL", "CAN", "HER", "WAS", "ONE", "OUR", "HAD", "BUT", "WHAT", "SAID", "EACH", "WHICH", "THEIR", "TIME", "WILL", "ABOUT", "IF", "UP", "OUT", "MANY", "THEN", "THEM", "THESE", "SO", "SOME", "HER", "WOULD", "MAKE", "LIKE", "INTO", "HIM", "HAS", "TWO", "MORE", "GO", "NO", "WAY", "COULD", "MY", "THAN", "FIRST", "BEEN", "CALL", "WHO", "ITS", "NOW", "FIND", "LONG", "DOWN", "DAY", "DID", "GET", "COME", "MADE", "MAY", "PART"}
        
        for symbol in symbols:
            if symbol not in common_words and len(symbol) <= 5:
                return symbol
        
        return None

    async def _gather_enhanced_market_context(self, message: str, intent: str) -> Dict[str, Any]:
        """Gather enhanced market context with real-time signals"""
        context = {}

        # Extract symbol if mentioned
        symbol = self._extract_symbol(message)
        if symbol:
            try:
                async with self.market_data:
                    # Get enhanced market context instead of basic data
                    enhanced_context = await self.market_data.get_enhanced_market_context(symbol)
                    context.update(enhanced_context)

            except Exception as e:
                self.logger.warning(f"Error gathering enhanced market context for {symbol}: {e}")

        return context

    async def _handle_enhanced_stock_analysis(self, message: str, context: Dict[str, Any]) -> AIResponse:
        """Enhanced stock analysis with rule validation"""
        if not context.get("symbol"):
            return AIResponse(
                response="Please specify a stock symbol for analysis (e.g., 'Analyze AAPL' or 'What's happening with TSLA?')",
                type="stock_analysis"
            )

        symbol = context["symbol"]

        if not self.openai_client:
            return self._fallback_enhanced_stock_analysis(symbol, context)

        try:
            # Build enhanced analysis prompt with real-time context
            prompt = self._build_enhanced_analysis_prompt(symbol, context)

            response = self.openai_client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": "You are A.T.L.A.S, an expert trading AI assistant. Provide educational and actionable trading analysis based on real-time market data. Always include risk management guidance."},
                    {"role": "user", "content": prompt}
                ],
                temperature=settings.OPENAI_TEMPERATURE,
                max_tokens=800
            )

            ai_response = response.choices[0].message.content.strip()

            # Validate any trading recommendations against rules
            if any(word in ai_response.lower() for word in ["buy", "sell", "enter", "long", "short"]):
                # Check if response follows trading rules
                rule_validation = await self._validate_trading_recommendation(ai_response, context)
                if rule_validation != RuleValidationResult.PASS:
                    ai_response += f"\n\n⚠️ Note: This recommendation requires additional validation. {rule_validation.value}"

            return AIResponse(
                response=ai_response,
                type="stock_analysis",
                function_called="enhanced_stock_analysis",
                confidence=0.8
            )

        except Exception as e:
            self.logger.error(f"Error in enhanced stock analysis: {e}")
            return self._fallback_enhanced_stock_analysis(symbol, context)

    def _build_enhanced_analysis_prompt(self, symbol: str, context: Dict[str, Any]) -> str:
        """Build enhanced analysis prompt with real-time market context"""
        prompt = f"Analyze {symbol} using the following real-time market data:\n\n"

        # Current price and movement
        if context.get("current_price"):
            prompt += f"Current Price: ${context['current_price']:.2f}\n"
            prompt += f"Change: {context.get('price_change', 0):+.2f} ({context.get('price_change_percent', 0):+.2f}%)\n\n"

        # Technical indicators
        prompt += "Technical Indicators:\n"
        if context.get("rsi"):
            prompt += f"- RSI: {context['rsi']:.1f}\n"
        if context.get("macd"):
            prompt += f"- MACD: {context['macd']:.3f}\n"
        if context.get("trend_direction"):
            prompt += f"- Trend Direction: {context['trend_direction']}\n"
        if context.get("volume_ratio"):
            prompt += f"- Volume Ratio: {context['volume_ratio']:.1f}x average\n"

        # Market signals
        prompt += "\nMarket Signals:\n"
        if context.get("ttm_squeeze"):
            prompt += f"- TTM Squeeze: {'Active' if context['ttm_squeeze'] else 'Inactive'}\n"
        if context.get("ema_alignment"):
            prompt += f"- EMA Alignment: {context['ema_alignment']}\n"
        if context.get("bb_position"):
            prompt += f"- Bollinger Band Position: {context['bb_position']}\n"

        # Support/Resistance
        if context.get("support_level") and context.get("resistance_level"):
            prompt += f"\nKey Levels:\n"
            prompt += f"- Support: ${context['support_level']:.2f}\n"
            prompt += f"- Resistance: ${context['resistance_level']:.2f}\n"

        # News sentiment
        news_sentiment = context.get("news_sentiment", {})
        if news_sentiment.get("overall"):
            prompt += f"\nNews Sentiment: {news_sentiment['overall']} (Score: {news_sentiment.get('score', 0):.2f})\n"

        prompt += "\nProvide a comprehensive analysis including:\n"
        prompt += "1. Technical outlook and trend analysis\n"
        prompt += "2. Key support/resistance levels\n"
        prompt += "3. Risk assessment and position sizing guidance\n"
        prompt += "4. Specific entry/exit strategies if applicable\n"
        prompt += "5. Educational explanation of the technical setup\n\n"
        prompt += "Always include proper risk management and never guarantee outcomes."

        return prompt

    async def _validate_trading_recommendation(self, response: str, context: Dict[str, Any]) -> RuleValidationResult:
        """Validate trading recommendation against custom rules"""
        try:
            # Extract trading signal from response if present
            signal_data = self._extract_signal_from_response(response, context)

            if not signal_data:
                return RuleValidationResult.PASS  # No specific signal to validate

            # Use trading rules engine to validate
            from models import TradingSignal, SignalType, TimeFrame

            signal = TradingSignal(
                symbol=context.get("symbol", ""),
                signal_type=SignalType.BUY if "buy" in response.lower() else SignalType.SELL,
                confidence=0.7,  # Default confidence
                entry_price=context.get("current_price", 0),
                timeframe=TimeFrame.DAY_1,
                reasoning=response[:200]  # First 200 chars as reasoning
            )

            validation_result, reason, details = self.trading_rules.validate_trading_signal(signal, context)
            return validation_result

        except Exception as e:
            self.logger.error(f"Error validating trading recommendation: {e}")
            return RuleValidationResult.WARNING

    def _extract_signal_from_response(self, response: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract trading signal data from AI response"""
        response_lower = response.lower()

        # Check if response contains trading action
        if not any(word in response_lower for word in ["buy", "sell", "enter", "exit", "long", "short"]):
            return None

        # Extract basic signal information
        signal_data = {
            "has_trading_action": True,
            "response_text": response,
            "context": context
        }

        return signal_data

    def _fallback_enhanced_stock_analysis(self, symbol: str, context: Dict[str, Any]) -> AIResponse:
        """Enhanced fallback analysis with real-time context"""
        price = context.get("current_price", 0)
        change = context.get("price_change", 0)
        change_percent = context.get("price_change_percent", 0)
        rsi = context.get("rsi")
        trend = context.get("trend_direction", "unknown")
        volume_ratio = context.get("volume_ratio", 0)

        response = f"📊 {symbol} Enhanced Analysis:\n\n"
        response += f"Current Price: ${price:.2f} ({change:+.2f}, {change_percent:+.2f}%)\n"
        response += f"Trend Direction: {trend.title()}\n"

        if rsi:
            if rsi > 70:
                response += f"RSI: {rsi:.1f} - Overbought territory, consider caution\n"
            elif rsi < 30:
                response += f"RSI: {rsi:.1f} - Oversold conditions, potential bounce\n"
            else:
                response += f"RSI: {rsi:.1f} - Neutral momentum\n"

        if volume_ratio > 1.5:
            response += f"Volume: {volume_ratio:.1f}x average - Strong conviction\n"
        elif volume_ratio < 0.8:
            response += f"Volume: {volume_ratio:.1f}x average - Low participation\n"

        # TTM Squeeze info
        if context.get("ttm_squeeze"):
            response += "\n🔥 TTM Squeeze is active - expect volatility expansion\n"

        # Support/Resistance
        support = context.get("support_level")
        resistance = context.get("resistance_level")
        if support and resistance:
            response += f"\nKey Levels:\n"
            response += f"Support: ${support:.2f}\n"
            response += f"Resistance: ${resistance:.2f}\n"

        response += "\n⚠️ Always use proper risk management and position sizing."

        return AIResponse(
            response=response,
            type="stock_analysis",
            confidence=0.6
        )

    async def _generate_corrected_response(self, message: str, context: Dict[str, Any], validation_reason: str) -> AIResponse:
        """Generate corrected response when validation fails"""
        try:
            corrected_prompt = f"""
            The previous response failed validation due to: {validation_reason}

            Please provide a corrected response to: "{message}"

            Market context: {context.get('symbol', 'N/A')} at ${context.get('current_price', 'N/A')}

            Ensure the response:
            1. Uses only valid technical indicators
            2. Includes proper risk management
            3. Avoids unrealistic claims
            4. Provides educational value
            5. Does not guarantee outcomes
            """

            if self.openai_client:
                response = self.openai_client.chat.completions.create(
                    model=settings.OPENAI_MODEL,
                    messages=[
                        {"role": "system", "content": "You are A.T.L.A.S, a responsible trading AI. Provide accurate, educational responses with proper risk management."},
                        {"role": "user", "content": corrected_prompt}
                    ],
                    temperature=0.1,  # Lower temperature for more conservative response
                    max_tokens=600
                )

                return AIResponse(
                    response=response.choices[0].message.content.strip(),
                    type="corrected_response",
                    validation_passed=True,
                    validation_reason="Response corrected after validation failure"
                )
            else:
                return AIResponse(
                    response="I apologize, but I need to provide a more accurate response. Please ask your question again with specific details about the stock symbol and what analysis you're looking for.",
                    type="corrected_response",
                    validation_passed=True
                )

        except Exception as e:
            self.logger.error(f"Error generating corrected response: {e}")
            return AIResponse(
                response="I encountered an issue providing a corrected response. Please rephrase your question.",
                type="error",
                validation_passed=False
            )
    
    async def _handle_stock_analysis(self, message: str, context: Dict[str, Any]) -> AIResponse:
        """Handle stock analysis requests"""
        if not context.get("symbol"):
            return AIResponse(
                response="Please specify a stock symbol for analysis (e.g., 'Analyze AAPL' or 'What's happening with TSLA?')",
                type="stock_analysis"
            )
        
        symbol = context["symbol"]
        quote = context.get("quote", {})
        
        if not self.openai_client:
            return self._fallback_stock_analysis(symbol, quote)
        
        try:
            # Build analysis prompt
            prompt = AI_PROMPTS["stock_analysis"].format(
                symbol=symbol,
                price=quote.get("price", "N/A"),
                indicators="Basic indicators calculated",  # Simplified for now
                sentiment="Neutral"  # Simplified for now
            )
            
            response = self.openai_client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": "You are A.T.L.A.S, an expert trading AI assistant. Provide educational and actionable trading analysis."},
                    {"role": "user", "content": prompt}
                ],
                temperature=settings.OPENAI_TEMPERATURE,
                max_tokens=800
            )
            
            return AIResponse(
                response=response.choices[0].message.content.strip(),
                type="stock_analysis",
                function_called="stock_analysis"
            )
            
        except Exception as e:
            self.logger.error(f"Error in stock analysis: {e}")
            return self._fallback_stock_analysis(symbol, quote)
    
    def _fallback_stock_analysis(self, symbol: str, quote: Dict[str, Any]) -> AIResponse:
        """Fallback stock analysis without LLM"""
        price = quote.get("price", 0)
        change = quote.get("change", 0)
        change_percent = quote.get("change_percent", 0)
        
        direction = "up" if change > 0 else "down" if change < 0 else "flat"
        
        response = f"""📊 {symbol} Analysis:

Current Price: ${price:.2f} ({change:+.2f}, {change_percent:+.2f}%)

The stock is {direction} today. """
        
        if abs(change_percent) > 3:
            response += "This is a significant move that warrants attention. "
        elif abs(change_percent) < 1:
            response += "This is normal daily volatility. "
        
        response += "\n\nFor detailed technical analysis, I recommend checking the charts and key support/resistance levels."
        
        return AIResponse(
            response=response,
            type="stock_analysis"
        )
    
    async def _handle_education_query(self, message: str) -> AIResponse:
        """Handle educational queries using RAG system"""
        try:
            from models import EducationQuery
            
            query = EducationQuery(question=message)
            education_response = await self.education_rag.answer_education_query(query)
            
            return AIResponse(
                response=education_response.answer,
                type="education",
                function_called="education_rag"
            )
            
        except Exception as e:
            self.logger.error(f"Error in education query: {e}")
            return AIResponse(
                response="I can help explain trading concepts. Try asking about specific topics like 'What is RSI?' or 'Explain support and resistance'.",
                type="education"
            )
    
    async def _handle_market_explanation(self, message: str, context: Dict[str, Any]) -> AIResponse:
        """Handle market movement explanation requests"""
        symbol = context.get("symbol")
        if not symbol:
            return AIResponse(
                response="Please specify a stock symbol to explain its price movement (e.g., 'Why did AAPL drop today?')",
                type="market_explanation"
            )
        
        try:
            async with self.market_data:
                explanation_data = await self.market_data.explain_price_movement(symbol)
            
            if not explanation_data:
                return AIResponse(
                    response=f"I couldn't gather enough information to explain {symbol}'s price movement today.",
                    type="market_explanation"
                )
            
            movement = explanation_data.get("movement", {})
            explanation = explanation_data.get("explanation", "")
            
            response = f"📈 {symbol} Movement Explanation:\n\n{explanation}"
            
            # Add news context if available
            news_analysis = explanation_data.get("news_analysis", [])
            if news_analysis:
                response += "\n\n📰 Recent News Impact:"
                for news in news_analysis[:2]:
                    sentiment_emoji = "🟢" if news["sentiment"] > 0 else "🔴" if news["sentiment"] < 0 else "⚪"
                    response += f"\n{sentiment_emoji} {news['title']}"
            
            return AIResponse(
                response=response,
                type="market_explanation",
                function_called="explain_price_movement"
            )
            
        except Exception as e:
            self.logger.error(f"Error explaining market movement: {e}")
            return AIResponse(
                response=f"I encountered an error explaining {symbol}'s price movement. Please try again.",
                type="market_explanation"
            )
    
    async def _handle_technical_question(self, message: str, context: Dict[str, Any]) -> AIResponse:
        """Handle technical analysis questions"""
        # Extract technical indicator from message
        message_lower = message.lower()
        
        if "rsi" in message_lower:
            concept = "RSI (Relative Strength Index)"
        elif "macd" in message_lower:
            concept = "MACD (Moving Average Convergence Divergence)"
        elif "support" in message_lower or "resistance" in message_lower:
            concept = "Support and Resistance levels"
        elif "moving average" in message_lower or "ma" in message_lower:
            concept = "Moving Averages"
        else:
            concept = "technical analysis"
        
        try:
            explanation = await self.education_rag.explain_trading_concept(concept)
            
            # Add current data if symbol is provided
            symbol = context.get("symbol")
            if symbol and context.get("quote"):
                quote = context["quote"]
                explanation += f"\n\n📊 Current {symbol} data:\nPrice: ${quote.get('price', 'N/A'):.2f}\nChange: {quote.get('change_percent', 0):+.2f}%"
            
            return AIResponse(
                response=explanation,
                type="technical_question",
                function_called="explain_concept"
            )
            
        except Exception as e:
            self.logger.error(f"Error handling technical question: {e}")
            return AIResponse(
                response=f"I can explain {concept}. It's a technical indicator used to analyze price movements and identify trading opportunities.",
                type="technical_question"
            )
    
    async def _handle_general_chat(self, message: str, context: Dict[str, Any]) -> AIResponse:
        """Handle general chat messages"""
        if not self.openai_client:
            return AIResponse(
                response="I'm A.T.L.A.S, your AI trading assistant. I can help with stock analysis, explain trading concepts, and answer market questions. What would you like to know?",
                type="general_chat"
            )
        
        try:
            # Build conversation context
            recent_messages = self.conversation_history[-6:]  # Last 6 messages
            messages = [
                {"role": "system", "content": "You are A.T.L.A.S, a helpful AI trading assistant. Keep responses concise and trading-focused."}
            ]
            
            for msg in recent_messages:
                messages.append({"role": msg.role, "content": msg.content})
            
            messages.append({"role": "user", "content": message})
            
            response = self.openai_client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=messages,
                temperature=settings.OPENAI_TEMPERATURE,
                max_tokens=400
            )
            
            return AIResponse(
                response=response.choices[0].message.content.strip(),
                type="general_chat"
            )
            
        except Exception as e:
            self.logger.error(f"Error in general chat: {e}")
            return AIResponse(
                response="I'm here to help with trading questions and analysis. What would you like to know about the markets?",
                type="general_chat"
            )
    
    async def analyze_sentiment(self, symbol: str, news_articles: List[NewsArticle]) -> SentimentAnalysis:
        """Analyze sentiment from news articles"""
        if not news_articles:
            return SentimentAnalysis(
                symbol=symbol,
                overall_sentiment="neutral",
                score=0.0,
                confidence=0.0,
                news_count=0,
                key_themes=[]
            )
        
        # Calculate basic sentiment score
        total_score = sum(article.sentiment_score or 0 for article in news_articles)
        avg_score = total_score / len(news_articles)
        
        # Determine overall sentiment
        if avg_score > 0.2:
            overall_sentiment = "bullish"
        elif avg_score < -0.2:
            overall_sentiment = "bearish"
        else:
            overall_sentiment = "neutral"
        
        # Extract key themes (simplified)
        key_themes = []
        for article in news_articles:
            title_lower = article.title.lower()
            if "earnings" in title_lower:
                key_themes.append("earnings")
            elif "revenue" in title_lower:
                key_themes.append("revenue")
            elif "partnership" in title_lower or "deal" in title_lower:
                key_themes.append("partnerships")
            elif "upgrade" in title_lower or "downgrade" in title_lower:
                key_themes.append("analyst_ratings")
        
        return SentimentAnalysis(
            symbol=symbol,
            overall_sentiment=overall_sentiment,
            score=avg_score,
            confidence=min(len(news_articles) / 10.0, 1.0),  # More articles = higher confidence
            news_count=len(news_articles),
            key_themes=list(set(key_themes))[:5]
        )
    
    def clear_conversation_history(self):
        """Clear conversation history"""
        self.conversation_history = []
        self.logger.info("Conversation history cleared")
    
    def get_conversation_summary(self) -> str:
        """Get summary of recent conversation"""
        if not self.conversation_history:
            return "No recent conversation."

        recent_topics = []
        for msg in self.conversation_history[-10:]:
            if msg.role == "user" and len(msg.content) > 10:
                recent_topics.append(msg.content[:50] + "..." if len(msg.content) > 50 else msg.content)

        return f"Recent topics discussed: {', '.join(recent_topics[-3:])}"

    async def calculate_ai_stop_loss(self, symbol: str, entry_price: float, direction: str,
                                   risk_percent: float = 2.0) -> Dict[str, Any]:
        """Calculate AI-enhanced stop loss"""
        try:
            async with self.market_data:
                # Get historical data for analysis
                historical_data = await self.market_data.get_historical_data(symbol, "1Day", 30)
                quote = await self.market_data.get_real_time_quote(symbol)

            if not historical_data or len(historical_data) < 10:
                # Fallback to simple percentage stop
                fallback_stop = entry_price * (0.98 if direction == "long" else 1.02)
                return {
                    "stop_price": fallback_stop,
                    "method": "percentage_fallback",
                    "risk_amount": abs(entry_price - fallback_stop),
                    "risk_percent": risk_percent,
                    "confidence": 0.5
                }

            # Calculate support/resistance levels
            recent_lows = [bar.low for bar in historical_data[-20:]]
            recent_highs = [bar.high for bar in historical_data[-20:]]

            if direction.lower() == "long":
                # For long positions, find support level
                support_level = min(recent_lows)
                atr_stop = entry_price - (sum(bar.high - bar.low for bar in historical_data[-14:]) / 14) * 1.5

                # Use the higher of support or ATR-based stop
                stop_price = max(support_level, atr_stop)

                # Ensure stop doesn't exceed risk tolerance
                max_loss = entry_price * (risk_percent / 100)
                min_stop = entry_price - max_loss
                stop_price = max(stop_price, min_stop)

            else:  # short position
                # For short positions, find resistance level
                resistance_level = max(recent_highs)
                atr_stop = entry_price + (sum(bar.high - bar.low for bar in historical_data[-14:]) / 14) * 1.5

                # Use the lower of resistance or ATR-based stop
                stop_price = min(resistance_level, atr_stop)

                # Ensure stop doesn't exceed risk tolerance
                max_loss = entry_price * (risk_percent / 100)
                max_stop = entry_price + max_loss
                stop_price = min(stop_price, max_stop)

            risk_amount = abs(entry_price - stop_price)
            actual_risk_percent = (risk_amount / entry_price) * 100

            return {
                "stop_price": round(stop_price, 2),
                "method": "ai_enhanced",
                "risk_amount": round(risk_amount, 2),
                "risk_percent": round(actual_risk_percent, 2),
                "confidence": 0.8,
                "support_resistance": support_level if direction == "long" else resistance_level
            }

        except Exception as e:
            self.logger.error(f"Error calculating AI stop loss: {e}")
            # Fallback calculation
            fallback_stop = entry_price * (0.98 if direction == "long" else 1.02)
            return {
                "stop_price": fallback_stop,
                "method": "error_fallback",
                "risk_amount": abs(entry_price - fallback_stop),
                "risk_percent": 2.0,
                "confidence": 0.3,
                "error": str(e)
            }
