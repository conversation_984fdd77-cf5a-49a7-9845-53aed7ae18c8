"""
Streamlined A.T.L.A.S Trading System - Technical Analysis & Scanner
Consolidated technical indicators, pattern recognition, and stock screening
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import math

from config import settings, POPULAR_SYMBOLS, TECHNICAL_PARAMS, SCAN_FILTERS
from models import OHLCV, TechnicalIndicators, ScanResult, TradingSignal, SignalType, TimeFrame
from market_data import MarketDataService


class TechnicalAnalysisEngine:
    """Comprehensive technical analysis with indicators and pattern recognition"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def calculate_indicators(self, data: List[OHLCV]) -> TechnicalIndicators:
        """Calculate all technical indicators for given OHLCV data"""
        if len(data) < 20:
            return TechnicalIndicators()
        
        # Convert to numpy arrays for efficient calculation
        closes = np.array([bar.close for bar in data])
        highs = np.array([bar.high for bar in data])
        lows = np.array([bar.low for bar in data])
        volumes = np.array([bar.volume for bar in data])
        
        indicators = TechnicalIndicators()
        
        try:
            # RSI
            indicators.rsi = self._calculate_rsi(closes, TECHNICAL_PARAMS["rsi_period"])
            
            # MACD
            macd_line, signal_line, histogram = self._calculate_macd(
                closes, 
                TECHNICAL_PARAMS["macd_fast"],
                TECHNICAL_PARAMS["macd_slow"], 
                TECHNICAL_PARAMS["macd_signal"]
            )
            indicators.macd = macd_line
            indicators.macd_signal = signal_line
            indicators.macd_histogram = histogram
            
            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = self._calculate_bollinger_bands(
                closes, 
                TECHNICAL_PARAMS["bb_period"],
                TECHNICAL_PARAMS["bb_std"]
            )
            indicators.bb_upper = bb_upper
            indicators.bb_middle = bb_middle
            indicators.bb_lower = bb_lower
            
            # Moving Averages
            indicators.sma_20 = self._calculate_sma(closes, 20)
            indicators.ema_12 = self._calculate_ema(closes, 12)
            indicators.ema_26 = self._calculate_ema(closes, 26)
            
            # ATR
            indicators.atr = self._calculate_atr(highs, lows, closes, TECHNICAL_PARAMS["atr_period"])
            
            # Volume SMA
            indicators.volume_sma = self._calculate_sma(volumes, TECHNICAL_PARAMS["volume_sma_period"])
            
        except Exception as e:
            self.logger.error(f"Error calculating indicators: {e}")
        
        return indicators
    
    def _calculate_rsi(self, prices: np.ndarray, period: int = 14) -> Optional[float]:
        """Calculate RSI (Relative Strength Index)"""
        if len(prices) < period + 1:
            return None
        
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains[:period])
        avg_loss = np.mean(losses[:period])
        
        for i in range(period, len(deltas)):
            avg_gain = (avg_gain * (period - 1) + gains[i]) / period
            avg_loss = (avg_loss * (period - 1) + losses[i]) / period
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return float(rsi)
    
    def _calculate_macd(self, prices: np.ndarray, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[Optional[float], Optional[float], Optional[float]]:
        """Calculate MACD (Moving Average Convergence Divergence)"""
        if len(prices) < slow + signal:
            return None, None, None
        
        ema_fast = self._calculate_ema(prices, fast)
        ema_slow = self._calculate_ema(prices, slow)
        
        if ema_fast is None or ema_slow is None:
            return None, None, None
        
        macd_line = ema_fast - ema_slow
        
        # Calculate signal line (EMA of MACD)
        macd_values = []
        for i in range(len(prices) - slow + 1):
            if i >= signal - 1:
                ema_f = self._calculate_ema(prices[i-slow+1:i+1], fast)
                ema_s = self._calculate_ema(prices[i-slow+1:i+1], slow)
                if ema_f is not None and ema_s is not None:
                    macd_values.append(ema_f - ema_s)
        
        if len(macd_values) >= signal:
            signal_line = self._calculate_ema(np.array(macd_values), signal)
            histogram = macd_line - signal_line if signal_line else None
            return float(macd_line), float(signal_line) if signal_line else None, float(histogram) if histogram else None
        
        return float(macd_line), None, None
    
    def _calculate_bollinger_bands(self, prices: np.ndarray, period: int = 20, std_dev: float = 2.0) -> Tuple[Optional[float], Optional[float], Optional[float]]:
        """Calculate Bollinger Bands"""
        if len(prices) < period:
            return None, None, None
        
        sma = self._calculate_sma(prices, period)
        if sma is None:
            return None, None, None
        
        std = np.std(prices[-period:])
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        
        return float(upper), float(sma), float(lower)
    
    def _calculate_sma(self, prices: np.ndarray, period: int) -> Optional[float]:
        """Calculate Simple Moving Average"""
        if len(prices) < period:
            return None
        return float(np.mean(prices[-period:]))
    
    def _calculate_ema(self, prices: np.ndarray, period: int) -> Optional[float]:
        """Calculate Exponential Moving Average"""
        if len(prices) < period:
            return None
        
        multiplier = 2 / (period + 1)
        ema = prices[0]
        
        for price in prices[1:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))
        
        return float(ema)
    
    def _calculate_atr(self, highs: np.ndarray, lows: np.ndarray, closes: np.ndarray, period: int = 14) -> Optional[float]:
        """Calculate Average True Range"""
        if len(highs) < period + 1:
            return None
        
        true_ranges = []
        for i in range(1, len(highs)):
            tr1 = highs[i] - lows[i]
            tr2 = abs(highs[i] - closes[i-1])
            tr3 = abs(lows[i] - closes[i-1])
            true_ranges.append(max(tr1, tr2, tr3))
        
        if len(true_ranges) >= period:
            return float(np.mean(true_ranges[-period:]))
        return None


class TechnicalScanner:
    """Stock scanner with technical analysis filters"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.market_data = MarketDataService()
        self.ta_engine = TechnicalAnalysisEngine()
    
    async def scan_oversold_stocks(self, symbols: Optional[List[str]] = None) -> List[ScanResult]:
        """Scan for oversold stocks (RSI < 30) with volume confirmation"""
        symbols = symbols or POPULAR_SYMBOLS
        results = []
        
        async with self.market_data:
            tasks = [self._analyze_symbol_for_oversold(symbol) for symbol in symbols]
            scan_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for symbol, result in zip(symbols, scan_results):
                if isinstance(result, ScanResult):
                    results.append(result)
                elif not isinstance(result, Exception):
                    self.logger.warning(f"Unexpected result for {symbol}: {result}")
        
        # Sort by RSI (most oversold first)
        results.sort(key=lambda x: x.indicators.get("rsi", 100))
        return results[:settings.MAX_SCAN_RESULTS]
    
    async def _analyze_symbol_for_oversold(self, symbol: str) -> Optional[ScanResult]:
        """Analyze single symbol for oversold conditions"""
        try:
            # Get historical data and current quote
            historical_data = await self.market_data.get_historical_data(symbol, "1Day", 50)
            quote = await self.market_data.get_real_time_quote(symbol)
            
            if len(historical_data) < 20:
                return None
            
            # Calculate indicators
            indicators = self.ta_engine.calculate_indicators(historical_data)
            
            # Check oversold criteria
            filters = SCAN_FILTERS["oversold"]
            if (indicators.rsi and indicators.rsi <= filters["rsi_max"] and
                indicators.volume_sma and quote.volume >= indicators.volume_sma * filters["volume_min_ratio"]):
                
                return ScanResult(
                    symbol=symbol,
                    scan_type="oversold",
                    score=100 - indicators.rsi,  # Lower RSI = higher score
                    current_price=quote.price,
                    indicators={
                        "rsi": indicators.rsi,
                        "volume_ratio": quote.volume / indicators.volume_sma if indicators.volume_sma else 0,
                        "price_change": quote.change_percent
                    },
                    reasoning=f"RSI at {indicators.rsi:.1f} indicates oversold conditions with {quote.volume/indicators.volume_sma:.1f}x average volume"
                )
        
        except Exception as e:
            self.logger.warning(f"Error analyzing {symbol} for oversold: {e}")
            return None
    
    async def scan_breakout_patterns(self, symbols: Optional[List[str]] = None) -> List[ScanResult]:
        """Scan for breakout patterns above resistance with volume"""
        symbols = symbols or POPULAR_SYMBOLS
        results = []
        
        async with self.market_data:
            tasks = [self._analyze_symbol_for_breakout(symbol) for symbol in symbols]
            scan_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for symbol, result in zip(symbols, scan_results):
                if isinstance(result, ScanResult):
                    results.append(result)
        
        # Sort by breakout strength
        results.sort(key=lambda x: x.score, reverse=True)
        return results[:settings.MAX_SCAN_RESULTS]
    
    async def _analyze_symbol_for_breakout(self, symbol: str) -> Optional[ScanResult]:
        """Analyze single symbol for breakout patterns"""
        try:
            historical_data = await self.market_data.get_historical_data(symbol, "1Day", 50)
            quote = await self.market_data.get_real_time_quote(symbol)
            
            if len(historical_data) < 20:
                return None
            
            indicators = self.ta_engine.calculate_indicators(historical_data)
            
            # Calculate resistance level (highest high in last 20 days)
            recent_highs = [bar.high for bar in historical_data[-20:]]
            resistance = max(recent_highs[:-1])  # Exclude today
            
            # Check breakout criteria
            filters = SCAN_FILTERS["breakout"]
            volume_ratio = quote.volume / indicators.volume_sma if indicators.volume_sma else 0
            
            if (quote.price > resistance and
                volume_ratio >= filters["volume_min_ratio"] and
                indicators.rsi and indicators.rsi >= filters["rsi_min"]):
                
                breakout_strength = ((quote.price - resistance) / resistance) * 100
                
                return ScanResult(
                    symbol=symbol,
                    scan_type="breakout",
                    score=breakout_strength * volume_ratio,
                    current_price=quote.price,
                    indicators={
                        "resistance": resistance,
                        "breakout_percent": breakout_strength,
                        "volume_ratio": volume_ratio,
                        "rsi": indicators.rsi
                    },
                    reasoning=f"Breakout above ${resistance:.2f} resistance with {volume_ratio:.1f}x volume"
                )
        
        except Exception as e:
            self.logger.warning(f"Error analyzing {symbol} for breakout: {e}")
            return None
    
    async def scan_ttm_squeeze(self, symbols: Optional[List[str]] = None) -> List[ScanResult]:
        """Scan for TTM Squeeze conditions (Bollinger Bands inside Keltner Channels)"""
        symbols = symbols or POPULAR_SYMBOLS
        results = []
        
        async with self.market_data:
            tasks = [self._analyze_symbol_for_squeeze(symbol) for symbol in symbols]
            scan_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for symbol, result in zip(symbols, scan_results):
                if isinstance(result, ScanResult):
                    results.append(result)
        
        results.sort(key=lambda x: x.score, reverse=True)
        return results[:settings.MAX_SCAN_RESULTS]
    
    async def _analyze_symbol_for_squeeze(self, symbol: str) -> Optional[ScanResult]:
        """Enhanced TTM Squeeze analysis with profit optimization features"""
        try:
            # Get multi-timeframe data for enhanced analysis
            daily_data = await self.market_data.get_historical_data(symbol, "1Day", 50)
            hourly_data = await self.market_data.get_historical_data(symbol, "1Hour", 100)
            quote = await self.market_data.get_real_time_quote(symbol)

            if len(daily_data) < 20 or len(hourly_data) < 20:
                return None

            # Calculate indicators for both timeframes
            daily_indicators = self.ta_engine.calculate_indicators(daily_data)
            hourly_indicators = self.ta_engine.calculate_indicators(hourly_data)

            # Enhanced TTM Squeeze analysis
            squeeze_analysis = await self._enhanced_squeeze_analysis(
                symbol, daily_data, hourly_data, daily_indicators, hourly_indicators, quote
            )

            if not squeeze_analysis:
                return None

            # Calculate profit-optimized signal strength
            signal_strength = self._calculate_enhanced_signal_strength(squeeze_analysis)

            if signal_strength < 0.65:  # Higher threshold for profit optimization
                return None

            return ScanResult(
                symbol=symbol,
                scan_type="ttm_squeeze_enhanced",
                score=signal_strength * 100,
                current_price=quote.price,
                indicators=squeeze_analysis["indicators"],
                reasoning=squeeze_analysis["reasoning"]
            )

        except Exception as e:
            self.logger.warning(f"Error in enhanced squeeze analysis for {symbol}: {e}")
            return None

    async def _enhanced_squeeze_analysis(self, symbol: str, daily_data: pd.DataFrame,
                                       hourly_data: pd.DataFrame, daily_indicators: Any,
                                       hourly_indicators: Any, quote: Any) -> Optional[Dict]:
        """Comprehensive TTM Squeeze analysis with profit optimization"""
        try:
            # Calculate Keltner Channels for both timeframes
            daily_kc_upper = daily_indicators.sma_20 + (daily_indicators.atr * 1.5)
            daily_kc_lower = daily_indicators.sma_20 - (daily_indicators.atr * 1.5)

            hourly_kc_upper = hourly_indicators.sma_20 + (hourly_indicators.atr * 1.5)
            hourly_kc_lower = hourly_indicators.sma_20 - (hourly_indicators.atr * 1.5)

            # Check squeeze conditions on both timeframes
            daily_squeeze = (daily_indicators.bb_upper < daily_kc_upper and
                           daily_indicators.bb_lower > daily_kc_lower)
            hourly_squeeze = (hourly_indicators.bb_upper < hourly_kc_upper and
                            hourly_indicators.bb_lower > hourly_kc_lower)

            if not (daily_squeeze or hourly_squeeze):
                return None

            # Calculate squeeze metrics
            daily_squeeze_ratio = ((daily_indicators.bb_upper - daily_indicators.bb_lower) /
                                 (daily_kc_upper - daily_kc_lower)) if daily_kc_upper != daily_kc_lower else 0

            hourly_squeeze_ratio = ((hourly_indicators.bb_upper - hourly_indicators.bb_lower) /
                                  (hourly_kc_upper - hourly_kc_lower)) if hourly_kc_upper != hourly_kc_lower else 0

            # Volume confirmation with weighted analysis
            volume_ratio = quote.volume / daily_indicators.volume_sma if daily_indicators.volume_sma else 0
            volume_acceleration = self._calculate_volume_acceleration(daily_data)

            # Momentum analysis with TTM histogram
            momentum_strength = self._calculate_momentum_strength(daily_data, hourly_data)

            # Multi-timeframe confluence scoring
            confluence_score = self._calculate_timeframe_confluence(
                daily_squeeze, hourly_squeeze, daily_squeeze_ratio, hourly_squeeze_ratio
            )

            # Expected value calculation based on historical performance
            expected_value = self._calculate_expected_value(
                daily_squeeze_ratio, volume_ratio, momentum_strength
            )

            if expected_value <= 0:  # Only positive EV setups
                return None

            return {
                "indicators": {
                    "daily_squeeze_ratio": daily_squeeze_ratio,
                    "hourly_squeeze_ratio": hourly_squeeze_ratio,
                    "volume_ratio": volume_ratio,
                    "volume_acceleration": volume_acceleration,
                    "momentum_strength": momentum_strength,
                    "confluence_score": confluence_score,
                    "expected_value": expected_value,
                    "timeframe_alignment": daily_squeeze and hourly_squeeze
                },
                "reasoning": f"Enhanced TTM Squeeze: Daily ratio {daily_squeeze_ratio:.3f}, "
                           f"Hourly ratio {hourly_squeeze_ratio:.3f}, "
                           f"Volume acceleration {volume_acceleration:.2f}, "
                           f"Expected Value: {expected_value:.3f}"
            }

        except Exception as e:
            self.logger.error(f"Error in enhanced squeeze analysis: {e}")
            return None

    def _calculate_enhanced_signal_strength(self, squeeze_analysis: Dict) -> float:
        """Calculate profit-optimized signal strength"""
        try:
            indicators = squeeze_analysis["indicators"]

            # Base strength from squeeze compression (tighter = stronger)
            daily_strength = max(0, 1 - indicators["daily_squeeze_ratio"]) * 0.3
            hourly_strength = max(0, 1 - indicators["hourly_squeeze_ratio"]) * 0.2

            # Volume confirmation strength
            volume_strength = min(1.0, indicators["volume_ratio"] / 2.0) * 0.2
            volume_accel_strength = min(1.0, indicators["volume_acceleration"] / 3.0) * 0.1

            # Momentum strength
            momentum_strength = indicators["momentum_strength"] * 0.15

            # Confluence bonus
            confluence_bonus = indicators["confluence_score"] * 0.05

            total_strength = (daily_strength + hourly_strength + volume_strength +
                            volume_accel_strength + momentum_strength + confluence_bonus)

            return min(1.0, total_strength)

        except Exception as e:
            self.logger.error(f"Error calculating enhanced signal strength: {e}")
            return 0.0

    def _calculate_volume_acceleration(self, data: pd.DataFrame) -> float:
        """Calculate volume acceleration over recent periods"""
        try:
            if len(data) < 10:
                return 0.0

            recent_volume = data['volume'].tail(5).mean()
            previous_volume = data['volume'].tail(10).head(5).mean()

            if previous_volume > 0:
                return recent_volume / previous_volume
            return 1.0

        except Exception as e:
            self.logger.error(f"Error calculating volume acceleration: {e}")
            return 0.0

    def _calculate_momentum_strength(self, daily_data: pd.DataFrame, hourly_data: pd.DataFrame) -> float:
        """Calculate momentum strength using TTM histogram and price action"""
        try:
            # Calculate TTM histogram for daily
            daily_momentum = self._calculate_ttm_histogram(daily_data)
            hourly_momentum = self._calculate_ttm_histogram(hourly_data)

            # Normalize momentum values
            daily_norm = np.tanh(daily_momentum / daily_data['close'].iloc[-1] * 1000) if len(daily_data) > 0 else 0
            hourly_norm = np.tanh(hourly_momentum / hourly_data['close'].iloc[-1] * 1000) if len(hourly_data) > 0 else 0

            # Combine with equal weighting
            combined_momentum = (daily_norm + hourly_norm) / 2

            # Convert to 0-1 scale
            return (combined_momentum + 1) / 2

        except Exception as e:
            self.logger.error(f"Error calculating momentum strength: {e}")
            return 0.0

    def _calculate_ttm_histogram(self, data: pd.DataFrame) -> float:
        """Calculate TTM histogram using linear regression slope"""
        try:
            if len(data) < 20:
                return 0.0

            # Use closing prices for linear regression
            prices = data['close'].tail(20).values
            x = np.arange(len(prices))

            # Calculate linear regression slope
            slope = np.polyfit(x, prices, 1)[0]
            return slope

        except Exception as e:
            self.logger.error(f"Error calculating TTM histogram: {e}")
            return 0.0

    def _calculate_timeframe_confluence(self, daily_squeeze: bool, hourly_squeeze: bool,
                                      daily_ratio: float, hourly_ratio: float) -> float:
        """Calculate multi-timeframe confluence score"""
        try:
            confluence = 0.0

            # Both timeframes in squeeze
            if daily_squeeze and hourly_squeeze:
                confluence += 0.5
            elif daily_squeeze or hourly_squeeze:
                confluence += 0.3

            # Squeeze tightness alignment
            if daily_ratio < 0.8 and hourly_ratio < 0.8:
                confluence += 0.3
            elif daily_ratio < 0.9 or hourly_ratio < 0.9:
                confluence += 0.2

            return min(1.0, confluence)

        except Exception as e:
            self.logger.error(f"Error calculating timeframe confluence: {e}")
            return 0.0

    def _calculate_expected_value(self, squeeze_ratio: float, volume_ratio: float,
                                momentum_strength: float) -> float:
        """Calculate expected value based on historical TTM Squeeze performance"""
        try:
            # Historical TTM Squeeze statistics (from backtesting)
            base_win_rate = 0.65
            base_avg_win = 0.08
            base_avg_loss = 0.03

            # Adjust win rate based on setup quality
            quality_multiplier = 1.0

            # Tighter squeeze = higher win rate
            if squeeze_ratio < 0.7:
                quality_multiplier += 0.1
            elif squeeze_ratio < 0.8:
                quality_multiplier += 0.05

            # High volume = higher win rate
            if volume_ratio > 2.0:
                quality_multiplier += 0.1
            elif volume_ratio > 1.5:
                quality_multiplier += 0.05

            # Strong momentum = higher win rate and bigger wins
            if momentum_strength > 0.7:
                quality_multiplier += 0.1
                base_avg_win *= 1.2
            elif momentum_strength > 0.6:
                quality_multiplier += 0.05
                base_avg_win *= 1.1

            adjusted_win_rate = min(0.85, base_win_rate * quality_multiplier)
            adjusted_loss_rate = 1 - adjusted_win_rate

            # Expected Value = P(win) * Avg_Win - P(loss) * Avg_Loss
            expected_value = (adjusted_win_rate * base_avg_win) - (adjusted_loss_rate * base_avg_loss)

            return expected_value

        except Exception as e:
            self.logger.error(f"Error calculating expected value: {e}")
            return 0.0
    
    async def scan_macd_bullish(self, symbols: Optional[List[str]] = None) -> List[ScanResult]:
        """Scan for bullish MACD crossovers"""
        symbols = symbols or POPULAR_SYMBOLS
        results = []
        
        async with self.market_data:
            tasks = [self._analyze_symbol_for_macd(symbol) for symbol in symbols]
            scan_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for symbol, result in zip(symbols, scan_results):
                if isinstance(result, ScanResult):
                    results.append(result)
        
        results.sort(key=lambda x: x.score, reverse=True)
        return results[:settings.MAX_SCAN_RESULTS]
    
    async def _analyze_symbol_for_macd(self, symbol: str) -> Optional[ScanResult]:
        """Analyze single symbol for MACD bullish crossover"""
        try:
            historical_data = await self.market_data.get_historical_data(symbol, "1Day", 50)
            quote = await self.market_data.get_real_time_quote(symbol)
            
            if len(historical_data) < 30:
                return None
            
            indicators = self.ta_engine.calculate_indicators(historical_data)
            
            if not (indicators.macd and indicators.macd_signal and indicators.macd_histogram):
                return None
            
            # Check for bullish conditions
            filters = SCAN_FILTERS["macd_bullish"]
            if (indicators.macd > indicators.macd_signal and
                indicators.macd_histogram > 0):
                
                # Calculate signal strength
                signal_strength = abs(indicators.macd_histogram) / abs(indicators.macd) if indicators.macd != 0 else 0
                
                return ScanResult(
                    symbol=symbol,
                    scan_type="macd_bullish",
                    score=signal_strength * 100,
                    current_price=quote.price,
                    indicators={
                        "macd": indicators.macd,
                        "macd_signal": indicators.macd_signal,
                        "macd_histogram": indicators.macd_histogram,
                        "signal_strength": signal_strength
                    },
                    reasoning=f"MACD bullish crossover with {signal_strength:.3f} signal strength"
                )
        
        except Exception as e:
            self.logger.warning(f"Error analyzing {symbol} for MACD: {e}")
            return None
    
    async def run_comprehensive_scan(self) -> Dict[str, List[ScanResult]]:
        """Run all scan types and return comprehensive results"""
        try:
            # Run all scans concurrently
            oversold_task = self.scan_oversold_stocks()
            breakout_task = self.scan_breakout_patterns()
            squeeze_task = self.scan_ttm_squeeze()
            macd_task = self.scan_macd_bullish()
            
            oversold, breakout, squeeze, macd = await asyncio.gather(
                oversold_task, breakout_task, squeeze_task, macd_task
            )
            
            return {
                "oversold": oversold,
                "breakout": breakout,
                "ttm_squeeze": squeeze,
                "macd_bullish": macd
            }
            
        except Exception as e:
            self.logger.error(f"Error in comprehensive scan: {e}")
            return {}
