"""
Streamlined A.T.L.A.S Trading System - Configuration
Consolidated configuration management for all system components
"""

import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Consolidated application settings"""
    
    # Application Settings
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    DEBUG: bool = Field(default=True, env="DEBUG")
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    PORT: int = Field(default=8080, env="PORT")
    
    # Alpaca Trading API
    APCA_API_BASE_URL: str = Field(env="APCA_API_BASE_URL")
    APCA_API_KEY_ID: str = Field(env="APCA_API_KEY_ID")
    APCA_API_SECRET_KEY: str = Field(env="APCA_API_SECRET_KEY")
    
    # Financial Modeling Prep API
    FMP_API_KEY: str = Field(env="FMP_API_KEY")
    FMP_BASE_URL: str = Field(default="https://financialmodelingprep.com/api", env="FMP_BASE_URL")
    
    # OpenAI API
    OPENAI_API_KEY: str = Field(env="OPENAI_API_KEY")
    OPENAI_MODEL: str = Field(default="gpt-4", env="OPENAI_MODEL")
    OPENAI_TEMPERATURE: float = Field(default=0.2, env="OPENAI_TEMPERATURE")

    # Predicto API (Enhanced Market Intelligence)
    PREDICTO_API_KEY: Optional[str] = Field(default=None, env="PREDICTO_API_KEY")
    PREDICTO_BASE_URL: str = Field(default="https://api.predic.to", env="PREDICTO_BASE_URL")

    # Web Search APIs (Optional)
    GOOGLE_SEARCH_API_KEY: Optional[str] = Field(default=None, env="GOOGLE_SEARCH_API_KEY")
    GOOGLE_SEARCH_ENGINE_ID: Optional[str] = Field(default=None, env="GOOGLE_SEARCH_ENGINE_ID")
    BING_SEARCH_API_KEY: Optional[str] = Field(default=None, env="BING_SEARCH_API_KEY")
    
    # Trading Configuration
    DEFAULT_RISK_PERCENT: float = Field(default=2.0, env="DEFAULT_RISK_PERCENT")
    MAX_POSITIONS: int = Field(default=10, env="MAX_POSITIONS")
    PAPER_TRADING: bool = Field(default=True, env="PAPER_TRADING")
    
    # System Performance
    API_TIMEOUT: int = Field(default=30, env="API_TIMEOUT")
    CACHE_TTL: int = Field(default=300, env="CACHE_TTL")
    MAX_SCAN_RESULTS: int = Field(default=50, env="MAX_SCAN_RESULTS")
    
    # RAG Education System
    ENABLE_RAG: bool = Field(default=True, env="ENABLE_RAG")
    VECTOR_DB_PATH: str = Field(default="./vector_db", env="VECTOR_DB_PATH")
    CHUNK_SIZE: int = Field(default=1000, env="CHUNK_SIZE")
    CHUNK_OVERLAP: int = Field(default=200, env="CHUNK_OVERLAP")
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()


# Trading symbols for scanning
POPULAR_SYMBOLS = [
    "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "NFLX",
    "SPY", "QQQ", "IWM", "DIA", "VTI", "ARKK", "XLF", "XLE",
    "AMD", "INTC", "CRM", "ADBE", "PYPL", "SQ", "ROKU", "ZM",
    "BABA", "JD", "NIO", "PLTR", "COIN", "HOOD", "SOFI", "UPST"
]

# Technical analysis parameters
TECHNICAL_PARAMS = {
    "rsi_period": 14,
    "rsi_oversold": 30,
    "rsi_overbought": 70,
    "macd_fast": 12,
    "macd_slow": 26,
    "macd_signal": 9,
    "bb_period": 20,
    "bb_std": 2.0,
    "sma_periods": [20, 50, 200],
    "ema_periods": [12, 26],
    "atr_period": 14,
    "volume_sma_period": 20
}

# Scan filters
SCAN_FILTERS = {
    "oversold": {
        "rsi_max": 30,
        "volume_min_ratio": 1.5
    },
    "breakout": {
        "price_above_resistance": True,
        "volume_min_ratio": 2.0,
        "rsi_min": 50
    },
    "ttm_squeeze": {
        "bb_inside_kc": True,
        "volume_above_average": True
    },
    "macd_bullish": {
        "macd_above_signal": True,
        "macd_histogram_positive": True
    }
}

# News sentiment keywords
BULLISH_KEYWORDS = [
    "earnings beat", "revenue growth", "profit increase", "expansion",
    "partnership", "acquisition", "upgrade", "bullish", "positive",
    "strong results", "outperform", "buy rating", "price target raised"
]

BEARISH_KEYWORDS = [
    "earnings miss", "revenue decline", "loss", "layoffs", "downgrade",
    "bearish", "negative", "weak results", "underperform", "sell rating",
    "price target lowered", "investigation", "lawsuit", "recall"
]

# Trading book content for RAG system
TRADING_BOOKS = {
    "trading_in_the_zone": {
        "title": "Trading in the Zone",
        "author": "Mark Douglas",
        "key_concepts": ["psychology", "discipline", "probability", "mindset", "consistency"]
    },
    "market_wizards": {
        "title": "Market Wizards", 
        "author": "Jack Schwager",
        "key_concepts": ["interviews", "strategies", "risk_management", "discipline", "success_stories"]
    },
    "technical_analysis_explained": {
        "title": "Technical Analysis Explained",
        "author": "Martin Pring", 
        "key_concepts": ["charts", "patterns", "indicators", "trends", "support_resistance"]
    },
    "how_to_make_money_in_stocks": {
        "title": "How to Make Money in Stocks",
        "author": "William O'Neil",
        "key_concepts": ["can_slim", "growth_stocks", "breakouts", "volume", "earnings"]
    },
    "new_trading_for_living": {
        "title": "The New Trading for a Living",
        "author": "Alexander Elder",
        "key_concepts": ["triple_screen", "psychology", "money_management", "systems", "discipline"]
    }
}

# AI prompt templates
AI_PROMPTS = {
    "stock_analysis": """
    You are A.T.L.A.S, an expert trading AI. Analyze {symbol} with the following data:
    Price: ${price}
    Technical Indicators: {indicators}
    News Sentiment: {sentiment}
    
    Provide a comprehensive analysis including:
    1. Technical outlook
    2. Risk assessment  
    3. Trading recommendation
    4. Key levels to watch
    
    Be educational and explain your reasoning.
    """,
    
    "chart_pattern": """
    Explain the {pattern} pattern on {symbol} in educational terms.
    Current price: ${price}
    Pattern details: {details}
    
    Cover:
    1. What this pattern means
    2. Typical price targets
    3. Risk management
    4. Success probability
    """,
    
    "market_event": """
    Explain why {symbol} moved {direction} by {percent}% today.
    News: {news}
    Market context: {context}
    
    Provide:
    1. Primary cause
    2. Market reaction analysis
    3. Trading implications
    4. What to watch next
    """,
    
    "education": """
    You are a trading educator. Answer this question using knowledge from trading books:
    Question: {question}
    
    Provide a clear, educational explanation that a beginner can understand.
    Include practical examples and actionable insights.
    """
}

# API endpoints
API_ENDPOINTS = {
    "alpaca": {
        "account": "/v2/account",
        "positions": "/v2/positions", 
        "orders": "/v2/orders",
        "quotes": "/v2/stocks/{symbol}/quotes/latest",
        "bars": "/v2/stocks/{symbol}/bars"
    },
    "fmp": {
        "quote": "/v3/quote/{symbol}",
        "historical": "/v3/historical-price-full/{symbol}",
        "news": "/v3/stock_news",
        "profile": "/v3/profile/{symbol}",
        "ratios": "/v3/ratios/{symbol}"
    }
}

# Logging configuration
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        }
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "default",
            "level": settings.LOG_LEVEL
        }
    },
    "root": {
        "level": settings.LOG_LEVEL,
        "handlers": ["console"]
    }
}


def get_api_headers(service: str) -> dict:
    """Get API headers for different services"""
    if service == "alpaca":
        return {
            "APCA-API-KEY-ID": settings.APCA_API_KEY_ID,
            "APCA-API-SECRET-KEY": settings.APCA_API_SECRET_KEY,
            "Content-Type": "application/json"
        }
    elif service == "fmp":
        return {
            "Content-Type": "application/json"
        }
    elif service == "openai":
        return {
            "Authorization": f"Bearer {settings.OPENAI_API_KEY}",
            "Content-Type": "application/json"
        }
    else:
        return {"Content-Type": "application/json"}


def get_api_url(service: str, endpoint: str, **kwargs) -> str:
    """Build API URL for different services"""
    if service == "alpaca":
        base_url = settings.APCA_API_BASE_URL
        endpoint_template = API_ENDPOINTS["alpaca"][endpoint]
    elif service == "fmp":
        base_url = settings.FMP_BASE_URL
        endpoint_template = API_ENDPOINTS["fmp"][endpoint]
    else:
        raise ValueError(f"Unknown service: {service}")
    
    # Format endpoint with parameters
    formatted_endpoint = endpoint_template.format(**kwargs)
    
    # Add API key for FMP
    if service == "fmp":
        separator = "&" if "?" in formatted_endpoint else "?"
        formatted_endpoint += f"{separator}apikey={settings.FMP_API_KEY}"
    
    return f"{base_url}{formatted_endpoint}"


# Validation functions
def validate_symbol(symbol: str) -> str:
    """Validate and normalize stock symbol"""
    if not symbol or not isinstance(symbol, str):
        raise ValueError("Symbol must be a non-empty string")
    
    symbol = symbol.upper().strip()
    if not symbol.isalpha() or len(symbol) > 5:
        raise ValueError("Invalid symbol format")
    
    return symbol


def validate_timeframe(timeframe: str) -> str:
    """Validate timeframe parameter"""
    valid_timeframes = ["1Min", "5Min", "15Min", "30Min", "1Hour", "1Day"]
    if timeframe not in valid_timeframes:
        raise ValueError(f"Invalid timeframe. Must be one of: {valid_timeframes}")
    return timeframe
