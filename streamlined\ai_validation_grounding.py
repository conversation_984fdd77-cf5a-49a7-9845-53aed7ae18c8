"""
A.T.L.A.S AI Validation & Grounding System
Prevents hallucinations by tying all recommendations to real market data
"""

import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

@dataclass
class ValidationResult:
    """Result of AI response validation"""
    is_valid: bool
    confidence_score: float
    grounding_sources: List[str]
    warnings: List[str]
    corrections: List[str]
    market_data_cited: bool
    technical_indicators_cited: bool
    risk_warnings_present: bool

class AIValidationGroundingSystem:
    """
    System to validate AI responses and ensure they're grounded in real data
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Required elements for trading recommendations
        self.required_elements = {
            "trading_plan": ["risk_management", "position_sizing", "exit_strategy"],
            "symbol_analysis": ["technical_indicators", "price_data", "risk_assessment"],
            "profit_optimization": ["risk_limits", "diversification", "stop_losses"]
        }
        
        # Patterns that indicate hallucination
        self.hallucination_patterns = [
            r"guaranteed profit",
            r"100% success",
            r"risk-free",
            r"always works",
            r"never fails",
            r"secret strategy",
            r"insider information",
            r"market manipulation"
        ]
        
        # Required risk warnings
        self.risk_warning_patterns = [
            r"risk",
            r"loss",
            r"volatile",
            r"uncertain",
            r"paper trading",
            r"not financial advice"
        ]
        
        # Technical indicator patterns
        self.technical_patterns = [
            r"RSI", r"MACD", r"moving average", r"support", r"resistance",
            r"volume", r"momentum", r"trend", r"bollinger", r"fibonacci"
        ]
        
        # Market data patterns
        self.market_data_patterns = [
            r"\$\d+\.\d+", r"price", r"quote", r"volume", r"high", r"low",
            r"open", r"close", r"market cap", r"P/E ratio"
        ]

    def validate_response(self, response: str, response_type: str, 
                         market_data: Optional[Dict] = None) -> ValidationResult:
        """
        Validate an AI response for accuracy and grounding
        """
        warnings = []
        corrections = []
        grounding_sources = []
        
        # Check for hallucination patterns
        hallucination_detected = self._check_hallucinations(response)
        if hallucination_detected:
            warnings.extend(hallucination_detected)
        
        # Check for required elements based on response type
        missing_elements = self._check_required_elements(response, response_type)
        if missing_elements:
            warnings.extend([f"Missing required element: {elem}" for elem in missing_elements])
        
        # Check for market data citations
        market_data_cited = self._check_market_data_citations(response)
        
        # Check for technical indicator citations
        technical_indicators_cited = self._check_technical_indicators(response)
        
        # Check for risk warnings
        risk_warnings_present = self._check_risk_warnings(response)
        
        # Validate against real market data if provided
        if market_data:
            data_validation = self._validate_against_market_data(response, market_data)
            if not data_validation["valid"]:
                warnings.extend(data_validation["warnings"])
                corrections.extend(data_validation["corrections"])
            grounding_sources.extend(data_validation["sources"])
        
        # Calculate confidence score
        confidence_score = self._calculate_confidence_score(
            hallucination_detected, missing_elements, market_data_cited,
            technical_indicators_cited, risk_warnings_present
        )
        
        # Determine if response is valid
        is_valid = (
            not hallucination_detected and
            len(missing_elements) == 0 and
            confidence_score >= 0.7
        )
        
        return ValidationResult(
            is_valid=is_valid,
            confidence_score=confidence_score,
            grounding_sources=grounding_sources,
            warnings=warnings,
            corrections=corrections,
            market_data_cited=market_data_cited,
            technical_indicators_cited=technical_indicators_cited,
            risk_warnings_present=risk_warnings_present
        )

    def _check_hallucinations(self, response: str) -> List[str]:
        """Check for hallucination patterns in response"""
        warnings = []
        response_lower = response.lower()
        
        for pattern in self.hallucination_patterns:
            if re.search(pattern, response_lower):
                warnings.append(f"Potential hallucination detected: {pattern}")
        
        return warnings

    def _check_required_elements(self, response: str, response_type: str) -> List[str]:
        """Check for required elements based on response type"""
        if response_type not in self.required_elements:
            return []
        
        missing = []
        response_lower = response.lower()
        
        for element in self.required_elements[response_type]:
            element_patterns = {
                "risk_management": [r"risk", r"stop", r"loss", r"limit"],
                "position_sizing": [r"position", r"size", r"allocation", r"capital"],
                "exit_strategy": [r"exit", r"target", r"profit", r"stop"],
                "technical_indicators": [r"RSI", r"MACD", r"moving average", r"indicator"],
                "price_data": [r"price", r"quote", r"\$\d+"],
                "risk_assessment": [r"risk", r"volatile", r"uncertain"],
                "risk_limits": [r"limit", r"maximum", r"stop"],
                "diversification": [r"diversif", r"spread", r"multiple"],
                "stop_losses": [r"stop", r"loss", r"exit"]
            }
            
            patterns = element_patterns.get(element, [element])
            if not any(re.search(pattern, response_lower) for pattern in patterns):
                missing.append(element)
        
        return missing

    def _check_market_data_citations(self, response: str) -> bool:
        """Check if response cites real market data"""
        for pattern in self.market_data_patterns:
            if re.search(pattern, response, re.IGNORECASE):
                return True
        return False

    def _check_technical_indicators(self, response: str) -> bool:
        """Check if response mentions technical indicators"""
        for pattern in self.technical_patterns:
            if re.search(pattern, response, re.IGNORECASE):
                return True
        return False

    def _check_risk_warnings(self, response: str) -> bool:
        """Check if response includes appropriate risk warnings"""
        response_lower = response.lower()
        warning_count = sum(1 for pattern in self.risk_warning_patterns 
                          if re.search(pattern, response_lower))
        return warning_count >= 2  # Require at least 2 risk-related terms

    def _validate_against_market_data(self, response: str, market_data: Dict) -> Dict:
        """Validate response against real market data"""
        warnings = []
        corrections = []
        sources = []
        
        # Extract price mentions from response
        price_mentions = re.findall(r'\$(\d+\.?\d*)', response)
        
        if price_mentions and "current_price" in market_data:
            current_price = float(market_data["current_price"])
            sources.append(f"Current price: ${current_price}")
            
            for price_str in price_mentions:
                mentioned_price = float(price_str)
                price_diff = abs(mentioned_price - current_price) / current_price
                
                if price_diff > 0.1:  # More than 10% difference
                    warnings.append(f"Price ${mentioned_price} differs significantly from current ${current_price}")
                    corrections.append(f"Current market price is ${current_price}")
        
        # Validate volume claims
        if "volume" in response.lower() and "volume" in market_data:
            sources.append(f"Current volume: {market_data['volume']}")
        
        # Validate trend claims
        if any(word in response.lower() for word in ["bullish", "bearish", "trending"]):
            if "trend" in market_data:
                sources.append(f"Current trend: {market_data['trend']}")
        
        return {
            "valid": len(warnings) == 0,
            "warnings": warnings,
            "corrections": corrections,
            "sources": sources
        }

    def _calculate_confidence_score(self, hallucination_detected: List[str], 
                                  missing_elements: List[str],
                                  market_data_cited: bool,
                                  technical_indicators_cited: bool,
                                  risk_warnings_present: bool) -> float:
        """Calculate confidence score for the response"""
        score = 1.0
        
        # Deduct for hallucinations
        score -= len(hallucination_detected) * 0.3
        
        # Deduct for missing elements
        score -= len(missing_elements) * 0.1
        
        # Bonus for good practices
        if market_data_cited:
            score += 0.1
        if technical_indicators_cited:
            score += 0.1
        if risk_warnings_present:
            score += 0.1
        
        return max(0.0, min(1.0, score))

    def create_grounded_response(self, original_response: str, validation_result: ValidationResult,
                               market_data: Optional[Dict] = None) -> str:
        """Create a grounded version of the response with proper citations"""
        
        if validation_result.is_valid:
            return original_response
        
        grounded_response = original_response
        
        # Add market data citations if missing
        if not validation_result.market_data_cited and market_data:
            market_citation = f"\n\n📊 **Current Market Data**: "
            if "current_price" in market_data:
                market_citation += f"Price: ${market_data['current_price']} "
            if "volume" in market_data:
                market_citation += f"| Volume: {market_data['volume']} "
            if "change_percent" in market_data:
                market_citation += f"| Change: {market_data['change_percent']}%"
            
            grounded_response += market_citation
        
        # Add risk warnings if missing
        if not validation_result.risk_warnings_present:
            risk_warning = "\n\n⚠️ **Risk Warning**: All trading involves risk of loss. This is not financial advice. Always use proper risk management and consider paper trading first."
            grounded_response += risk_warning
        
        # Add corrections
        if validation_result.corrections:
            corrections_text = "\n\n🔧 **Data Corrections**: " + " | ".join(validation_result.corrections)
            grounded_response += corrections_text
        
        # Add grounding sources
        if validation_result.grounding_sources:
            sources_text = "\n\n📚 **Data Sources**: " + " | ".join(validation_result.grounding_sources)
            grounded_response += sources_text
        
        return grounded_response

    def get_fallback_response(self, intent: str) -> str:
        """Get a safe fallback response when validation fails"""
        fallback_responses = {
            "trading_plan": """
I'm having trouble accessing current market data to provide a specific trading plan. 

**Here's what I can suggest:**
1. **Check market conditions** - Ensure markets are open and liquid
2. **Review your risk tolerance** - Never risk more than you can afford to lose
3. **Use paper trading first** - Test strategies without real money
4. **Consult multiple sources** - Don't rely on a single analysis

**Next Steps:**
- Try asking for a specific stock analysis: "Analyze AAPL"
- Check market data: "Get SPY quote"
- Learn about risk management: "Explain position sizing"

⚠️ **Important**: This is educational content, not financial advice.
            """,
            "symbol_analysis": """
I'm unable to provide a complete analysis without current market data.

**To get accurate analysis:**
1. **Verify the symbol** - Make sure it's a valid ticker
2. **Check market hours** - Analysis is best during trading hours
3. **Try again** - Market data services may be temporarily unavailable

**Alternative actions:**
- Get a basic quote: "Get [SYMBOL] price"
- Learn about technical analysis: "Explain RSI indicator"
- Understand risk management: "How do stop losses work?"

⚠️ **Remember**: Never trade without current, accurate market data.
            """,
            "general": """
I'm experiencing some technical difficulties accessing market data right now.

**What you can do:**
- Try rephrasing your question
- Ask about trading education topics
- Check back in a few minutes

**Educational topics I can always help with:**
- Risk management principles
- Trading psychology
- Basic technical analysis concepts
- Position sizing strategies

⚠️ **Note**: Always verify any trading information with current market data.
            """
        }
        
        return fallback_responses.get(intent, fallback_responses["general"])
