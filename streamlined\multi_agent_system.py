"""
A.T.L.A.S Multi-Agent Trading System
Specialized agents for comprehensive market analysis and decision-making
Based on academic research: "Multi-Agent Trading Systems" (arXiv:2412.20138)
"""

import logging
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from abc import ABC, abstractmethod
from enum import Enum

from config import settings
from models import TechnicalIndicators, Quote, OHLCV


class AgentType(Enum):
    """Types of specialized trading agents"""
    TECHNICAL = "technical_analysis"
    RISK = "risk_management"
    SENTIMENT = "sentiment_analysis"
    EXECUTION = "execution_timing"


class AgentDecision(Enum):
    """Agent decision types"""
    STRONG_BUY = 1.0
    BUY = 0.5
    NEUTRAL = 0.0
    SELL = -0.5
    STRONG_SELL = -1.0


@dataclass
class AgentAnalysis:
    """Analysis result from a specialized agent"""
    agent_type: AgentType
    decision: AgentDecision
    confidence: float  # 0.0 to 1.0
    reasoning: str
    data_points: Dict[str, Any]
    risk_factors: List[str]
    timestamp: datetime


@dataclass
class AgentConsensus:
    """Consensus result from multiple agents"""
    final_decision: AgentDecision
    consensus_confidence: float
    agent_votes: Dict[AgentType, AgentAnalysis]
    disagreement_score: float
    risk_assessment: str
    execution_recommendation: str


class TradingAgent(ABC):
    """Abstract base class for all trading agents"""
    
    def __init__(self, agent_type: AgentType, weight: float = 1.0):
        self.agent_type = agent_type
        self.weight = weight
        self.logger = logging.getLogger(f"{__name__}.{agent_type.value}")
        
    @abstractmethod
    async def analyze(self, symbol: str, market_data: Dict[str, Any]) -> AgentAnalysis:
        """Perform specialized analysis and return decision"""
        pass
    
    def _calculate_confidence(self, signal_strength: float, data_quality: float) -> float:
        """Calculate confidence based on signal strength and data quality"""
        base_confidence = abs(signal_strength)
        quality_adjusted = base_confidence * data_quality
        return min(quality_adjusted, 1.0)


class TechnicalAnalysisAgent(TradingAgent):
    """Specialized agent for technical analysis and chart patterns"""
    
    def __init__(self):
        super().__init__(AgentType.TECHNICAL, weight=0.35)
        
    async def analyze(self, symbol: str, market_data: Dict[str, Any]) -> AgentAnalysis:
        """Analyze technical indicators and chart patterns"""
        
        indicators = market_data.get("indicators")
        historical_data = market_data.get("historical_data", [])
        current_price = market_data.get("current_price", 0)
        
        if not indicators:
            return self._create_low_confidence_analysis("Insufficient technical data")
        
        # Technical signal calculation
        signal_strength = 0.0
        reasoning_parts = []
        data_points = {}
        risk_factors = []
        
        # RSI Analysis
        if hasattr(indicators, 'rsi') and indicators.rsi:
            if indicators.rsi < 30:
                signal_strength += 0.3
                reasoning_parts.append(f"RSI oversold at {indicators.rsi:.1f} - bullish signal")
            elif indicators.rsi > 70:
                signal_strength -= 0.3
                reasoning_parts.append(f"RSI overbought at {indicators.rsi:.1f} - bearish signal")
                risk_factors.append("Overbought conditions may lead to pullback")
            else:
                reasoning_parts.append(f"RSI neutral at {indicators.rsi:.1f}")
            
            data_points["rsi"] = indicators.rsi
        
        # MACD Analysis
        if hasattr(indicators, 'macd') and hasattr(indicators, 'macd_signal'):
            if indicators.macd and indicators.macd_signal:
                macd_diff = indicators.macd - indicators.macd_signal
                if macd_diff > 0:
                    signal_strength += 0.25
                    reasoning_parts.append("MACD bullish crossover detected")
                else:
                    signal_strength -= 0.25
                    reasoning_parts.append("MACD bearish crossover detected")
                
                data_points["macd_signal"] = "bullish" if macd_diff > 0 else "bearish"
        
        # Bollinger Bands Analysis
        if hasattr(indicators, 'bb_upper') and hasattr(indicators, 'bb_lower'):
            if indicators.bb_upper and indicators.bb_lower:
                bb_width = (indicators.bb_upper - indicators.bb_lower) / indicators.bb_lower
                if bb_width < 0.05:  # Tight squeeze
                    signal_strength += 0.2
                    reasoning_parts.append("Bollinger Bands squeeze - breakout potential")
                elif current_price > indicators.bb_upper:
                    risk_factors.append("Price above upper Bollinger Band - potential reversal")
                elif current_price < indicators.bb_lower:
                    signal_strength += 0.15
                    reasoning_parts.append("Price below lower Bollinger Band - oversold")
                
                data_points["bb_position"] = self._get_bb_position(current_price, indicators)
        
        # Moving Average Analysis
        if hasattr(indicators, 'sma_20') and indicators.sma_20:
            if current_price > indicators.sma_20:
                signal_strength += 0.1
                reasoning_parts.append("Price above 20-day SMA - bullish trend")
            else:
                signal_strength -= 0.1
                reasoning_parts.append("Price below 20-day SMA - bearish trend")
            
            data_points["price_vs_sma20"] = (current_price - indicators.sma_20) / indicators.sma_20 * 100
        
        # Volume Analysis
        volume_ratio = market_data.get("volume_ratio", 1.0)
        if volume_ratio > 1.5:
            signal_strength += 0.1
            reasoning_parts.append(f"High volume confirmation ({volume_ratio:.1f}x average)")
        elif volume_ratio < 0.5:
            risk_factors.append("Low volume - weak signal confirmation")
            signal_strength *= 0.8  # Reduce signal strength
        
        data_points["volume_ratio"] = volume_ratio
        
        # Determine decision
        decision = self._signal_to_decision(signal_strength)
        confidence = self._calculate_confidence(signal_strength, self._assess_data_quality(indicators))
        
        reasoning = "Technical Analysis: " + "; ".join(reasoning_parts)
        
        return AgentAnalysis(
            agent_type=self.agent_type,
            decision=decision,
            confidence=confidence,
            reasoning=reasoning,
            data_points=data_points,
            risk_factors=risk_factors,
            timestamp=datetime.now()
        )
    
    def _create_low_confidence_analysis(self, reason: str) -> AgentAnalysis:
        """Create low confidence analysis when data is insufficient"""
        return AgentAnalysis(
            agent_type=self.agent_type,
            decision=AgentDecision.NEUTRAL,
            confidence=0.1,
            reasoning=f"Technical Analysis: {reason}",
            data_points={},
            risk_factors=["Insufficient technical data"],
            timestamp=datetime.now()
        )
    
    def _get_bb_position(self, price: float, indicators) -> str:
        """Get Bollinger Band position"""
        if price > indicators.bb_upper:
            return "above_upper"
        elif price < indicators.bb_lower:
            return "below_lower"
        elif price > indicators.bb_middle:
            return "upper_half"
        else:
            return "lower_half"
    
    def _signal_to_decision(self, signal_strength: float) -> AgentDecision:
        """Convert signal strength to agent decision"""
        if signal_strength >= 0.6:
            return AgentDecision.STRONG_BUY
        elif signal_strength >= 0.3:
            return AgentDecision.BUY
        elif signal_strength <= -0.6:
            return AgentDecision.STRONG_SELL
        elif signal_strength <= -0.3:
            return AgentDecision.SELL
        else:
            return AgentDecision.NEUTRAL
    
    def _assess_data_quality(self, indicators) -> float:
        """Assess quality of technical data"""
        quality_score = 0.0
        total_indicators = 0
        
        # Check availability of key indicators
        key_indicators = ['rsi', 'macd', 'macd_signal', 'bb_upper', 'bb_lower', 'sma_20']
        
        for indicator in key_indicators:
            total_indicators += 1
            if hasattr(indicators, indicator) and getattr(indicators, indicator) is not None:
                quality_score += 1
        
        return quality_score / total_indicators if total_indicators > 0 else 0.0


class RiskManagementAgent(TradingAgent):
    """Specialized agent for risk assessment and position sizing"""
    
    def __init__(self):
        super().__init__(AgentType.RISK, weight=0.30)
        
    async def analyze(self, symbol: str, market_data: Dict[str, Any]) -> AgentAnalysis:
        """Analyze risk factors and provide risk-adjusted recommendations"""
        
        signal_strength = 0.0
        reasoning_parts = []
        data_points = {}
        risk_factors = []
        
        # Volatility Assessment
        predicto_volatility = market_data.get("predicto_nasdaq_outlook", [{}])[0].get("forecasted_volatility", 0.15)
        if predicto_volatility < 0.12:  # Low volatility
            signal_strength += 0.3
            reasoning_parts.append(f"Low market volatility ({predicto_volatility*100:.1f}%) - favorable risk environment")
        elif predicto_volatility > 0.25:  # High volatility
            signal_strength -= 0.4
            reasoning_parts.append(f"High market volatility ({predicto_volatility*100:.1f}%) - elevated risk")
            risk_factors.append("High volatility increases position risk")
        
        data_points["market_volatility"] = predicto_volatility
        
        # Confidence Assessment
        predicto_confidence = market_data.get("predicto_confidence", 0.5)
        if predicto_confidence > 0.75:
            signal_strength += 0.2
            reasoning_parts.append(f"High AI prediction confidence ({predicto_confidence*100:.1f}%)")
        elif predicto_confidence < 0.4:
            signal_strength -= 0.3
            reasoning_parts.append(f"Low AI prediction confidence ({predicto_confidence*100:.1f}%)")
            risk_factors.append("Low prediction confidence suggests uncertain outcomes")
        
        data_points["prediction_confidence"] = predicto_confidence
        
        # Market Sentiment Risk
        market_sentiment = market_data.get("predicto_market_sentiment", "neutral")
        if market_sentiment == "uncertain":
            signal_strength -= 0.2
            risk_factors.append("Market sentiment uncertainty increases risk")
        elif market_sentiment in ["bullish", "bearish"]:
            signal_strength += 0.1
            reasoning_parts.append(f"Clear market sentiment ({market_sentiment}) reduces uncertainty")
        
        # ATR-based Risk Assessment
        indicators = market_data.get("indicators")
        if indicators and hasattr(indicators, 'atr') and indicators.atr:
            if indicators.atr < 1.5:  # Low ATR = lower risk
                signal_strength += 0.15
                reasoning_parts.append(f"Low ATR ({indicators.atr:.2f}) indicates stable price action")
            elif indicators.atr > 3.0:  # High ATR = higher risk
                signal_strength -= 0.15
                risk_factors.append(f"High ATR ({indicators.atr:.2f}) indicates volatile price action")
            
            data_points["atr"] = indicators.atr
        
        # Position Sizing Recommendation
        max_position_size = self._calculate_max_position_size(predicto_volatility, predicto_confidence)
        data_points["max_position_size_pct"] = max_position_size
        reasoning_parts.append(f"Recommended max position size: {max_position_size:.1f}% of account")
        
        decision = self._signal_to_decision(signal_strength)
        confidence = self._calculate_confidence(abs(signal_strength), 0.9)  # High data quality for risk assessment
        
        reasoning = "Risk Management: " + "; ".join(reasoning_parts)
        
        return AgentAnalysis(
            agent_type=self.agent_type,
            decision=decision,
            confidence=confidence,
            reasoning=reasoning,
            data_points=data_points,
            risk_factors=risk_factors,
            timestamp=datetime.now()
        )
    
    def _calculate_max_position_size(self, volatility: float, confidence: float) -> float:
        """Calculate maximum recommended position size"""
        # Base size from confidence
        base_size = confidence * 3.0  # Max 3% at 100% confidence
        
        # Volatility adjustment
        volatility_factor = max(0.5, 1.0 - (volatility - 0.15) * 2)  # Reduce size for high volatility
        
        adjusted_size = base_size * volatility_factor
        return min(adjusted_size, 2.5)  # Absolute cap at 2.5%
    
    def _signal_to_decision(self, signal_strength: float) -> AgentDecision:
        """Convert risk signal to decision (positive = lower risk = more bullish)"""
        if signal_strength >= 0.4:
            return AgentDecision.BUY  # Low risk environment
        elif signal_strength >= 0.1:
            return AgentDecision.NEUTRAL
        elif signal_strength <= -0.4:
            return AgentDecision.SELL  # High risk environment
        else:
            return AgentDecision.NEUTRAL


class SentimentAnalysisAgent(TradingAgent):
    """Specialized agent for market sentiment and news analysis"""

    def __init__(self):
        super().__init__(AgentType.SENTIMENT, weight=0.20)

    async def analyze(self, symbol: str, market_data: Dict[str, Any]) -> AgentAnalysis:
        """Analyze market sentiment and news factors"""

        signal_strength = 0.0
        reasoning_parts = []
        data_points = {}
        risk_factors = []

        # Predicto Market Sentiment
        market_sentiment = market_data.get("predicto_market_sentiment", "neutral")
        if market_sentiment == "bullish":
            signal_strength += 0.4
            reasoning_parts.append("Predicto AI detects bullish market sentiment")
        elif market_sentiment == "bearish":
            signal_strength -= 0.4
            reasoning_parts.append("Predicto AI detects bearish market sentiment")
        elif market_sentiment == "uncertain":
            signal_strength -= 0.1
            risk_factors.append("Market sentiment uncertainty creates unpredictable conditions")
        else:
            reasoning_parts.append("Neutral market sentiment detected")

        data_points["predicto_sentiment"] = market_sentiment

        # News Sentiment Analysis
        news_sentiment = market_data.get("news_sentiment", "neutral")
        news_count = market_data.get("news_count", 0)

        if news_count > 0:
            if news_sentiment == "positive":
                signal_strength += 0.3
                reasoning_parts.append(f"Positive news sentiment from {news_count} articles")
            elif news_sentiment == "negative":
                signal_strength -= 0.3
                reasoning_parts.append(f"Negative news sentiment from {news_count} articles")
                risk_factors.append("Negative news may pressure stock price")
            else:
                reasoning_parts.append(f"Neutral news sentiment from {news_count} articles")
        else:
            reasoning_parts.append("No recent news available for sentiment analysis")
            risk_factors.append("Lack of news coverage may indicate low market interest")

        data_points["news_sentiment"] = news_sentiment
        data_points["news_count"] = news_count

        # Nasdaq Outlook Integration
        nasdaq_outlook = market_data.get("predicto_nasdaq_outlook", [])
        if nasdaq_outlook:
            latest_outlook = nasdaq_outlook[0]
            outlook_score = latest_outlook.get("outlook_score", 0)

            if outlook_score > 0.3:
                signal_strength += 0.2
                reasoning_parts.append(f"Positive Nasdaq outlook score ({outlook_score:.2f})")
            elif outlook_score < -0.3:
                signal_strength -= 0.2
                reasoning_parts.append(f"Negative Nasdaq outlook score ({outlook_score:.2f})")
                risk_factors.append("Negative market outlook may affect individual stocks")

            data_points["nasdaq_outlook_score"] = outlook_score

        decision = self._signal_to_decision(signal_strength)
        confidence = self._calculate_confidence(abs(signal_strength), self._assess_sentiment_data_quality(market_data))

        reasoning = "Sentiment Analysis: " + "; ".join(reasoning_parts)

        return AgentAnalysis(
            agent_type=self.agent_type,
            decision=decision,
            confidence=confidence,
            reasoning=reasoning,
            data_points=data_points,
            risk_factors=risk_factors,
            timestamp=datetime.now()
        )

    def _assess_sentiment_data_quality(self, market_data: Dict[str, Any]) -> float:
        """Assess quality of sentiment data"""
        quality_score = 0.0

        # Check data availability
        if market_data.get("predicto_market_sentiment") != "neutral":
            quality_score += 0.3
        if market_data.get("news_count", 0) > 0:
            quality_score += 0.3
        if market_data.get("predicto_nasdaq_outlook"):
            quality_score += 0.4

        return min(quality_score, 1.0)

    def _signal_to_decision(self, signal_strength: float) -> AgentDecision:
        """Convert sentiment signal to decision"""
        if signal_strength >= 0.5:
            return AgentDecision.STRONG_BUY
        elif signal_strength >= 0.2:
            return AgentDecision.BUY
        elif signal_strength <= -0.5:
            return AgentDecision.STRONG_SELL
        elif signal_strength <= -0.2:
            return AgentDecision.SELL
        else:
            return AgentDecision.NEUTRAL


class ExecutionTimingAgent(TradingAgent):
    """Specialized agent for execution timing and market microstructure"""

    def __init__(self):
        super().__init__(AgentType.EXECUTION, weight=0.15)

    async def analyze(self, symbol: str, market_data: Dict[str, Any]) -> AgentAnalysis:
        """Analyze execution timing and market conditions"""

        signal_strength = 0.0
        reasoning_parts = []
        data_points = {}
        risk_factors = []

        # Volume Analysis
        volume_ratio = market_data.get("volume_ratio", 1.0)
        if volume_ratio > 2.0:
            signal_strength += 0.4
            reasoning_parts.append(f"Exceptional volume ({volume_ratio:.1f}x) - excellent execution conditions")
        elif volume_ratio > 1.5:
            signal_strength += 0.2
            reasoning_parts.append(f"High volume ({volume_ratio:.1f}x) - good execution conditions")
        elif volume_ratio < 0.5:
            signal_strength -= 0.3
            reasoning_parts.append(f"Low volume ({volume_ratio:.1f}x) - poor execution conditions")
            risk_factors.append("Low volume may result in poor fill prices and slippage")

        data_points["volume_ratio"] = volume_ratio

        # Market Hours Assessment
        current_hour = datetime.now().hour
        if 9 <= current_hour <= 11:  # Market open
            signal_strength += 0.2
            reasoning_parts.append("Market open hours - high liquidity period")
        elif 14 <= current_hour <= 16:  # Market close
            signal_strength += 0.15
            reasoning_parts.append("Market close hours - increased activity")
        elif 11 <= current_hour <= 14:  # Mid-day
            signal_strength += 0.05
            reasoning_parts.append("Mid-day trading - moderate liquidity")
        else:  # After hours
            signal_strength -= 0.2
            reasoning_parts.append("After-hours trading - reduced liquidity")
            risk_factors.append("After-hours trading has wider spreads and lower liquidity")

        data_points["market_hour"] = current_hour

        decision = self._signal_to_decision(signal_strength)
        confidence = self._calculate_confidence(abs(signal_strength), 0.8)

        reasoning = "Execution Timing: " + "; ".join(reasoning_parts)

        return AgentAnalysis(
            agent_type=self.agent_type,
            decision=decision,
            confidence=confidence,
            reasoning=reasoning,
            data_points=data_points,
            risk_factors=risk_factors,
            timestamp=datetime.now()
        )

    def _signal_to_decision(self, signal_strength: float) -> AgentDecision:
        """Convert execution signal to decision"""
        if signal_strength >= 0.4:
            return AgentDecision.BUY  # Excellent execution conditions
        elif signal_strength >= 0.1:
            return AgentDecision.NEUTRAL  # Acceptable conditions
        elif signal_strength <= -0.3:
            return AgentDecision.SELL  # Poor execution conditions
        else:
            return AgentDecision.NEUTRAL


class MultiAgentCoordinator:
    """
    Coordinates multiple specialized trading agents and produces consensus decisions
    Based on academic research: "Multi-Agent Trading Systems" (arXiv:2412.20138)
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Initialize specialized agents
        self.agents = {
            AgentType.TECHNICAL: TechnicalAnalysisAgent(),
            AgentType.RISK: RiskManagementAgent(),
            AgentType.SENTIMENT: SentimentAnalysisAgent(),
            AgentType.EXECUTION: ExecutionTimingAgent()
        }

        # Agent weights for consensus calculation
        self.agent_weights = {
            AgentType.TECHNICAL: 0.35,
            AgentType.RISK: 0.30,
            AgentType.SENTIMENT: 0.20,
            AgentType.EXECUTION: 0.15
        }

    async def analyze_symbol(self, symbol: str, market_data: Dict[str, Any]) -> AgentConsensus:
        """
        Coordinate all agents to analyze a symbol and produce consensus
        """

        try:
            # Get analysis from all agents
            agent_analyses = {}

            for agent_type, agent in self.agents.items():
                try:
                    analysis = await agent.analyze(symbol, market_data)
                    agent_analyses[agent_type] = analysis
                    self.logger.debug(f"{agent_type.value} analysis: {analysis.decision.name} ({analysis.confidence:.2f})")
                except Exception as e:
                    self.logger.error(f"Error in {agent_type.value} analysis: {e}")
                    # Create fallback neutral analysis
                    agent_analyses[agent_type] = AgentAnalysis(
                        agent_type=agent_type,
                        decision=AgentDecision.NEUTRAL,
                        confidence=0.1,
                        reasoning=f"Analysis failed: {str(e)}",
                        data_points={},
                        risk_factors=["Agent analysis failed"],
                        timestamp=datetime.now()
                    )

            # Calculate consensus
            consensus = self._calculate_consensus(agent_analyses)

            return consensus

        except Exception as e:
            self.logger.error(f"Error in multi-agent analysis for {symbol}: {e}")
            # Return safe fallback consensus
            return self._create_fallback_consensus(agent_analyses if 'agent_analyses' in locals() else {})

    def _calculate_consensus(self, agent_analyses: Dict[AgentType, AgentAnalysis]) -> AgentConsensus:
        """Calculate weighted consensus from agent analyses"""

        # Calculate weighted decision score
        total_weighted_score = 0.0
        total_weight = 0.0
        total_confidence = 0.0
        all_risk_factors = []

        for agent_type, analysis in agent_analyses.items():
            weight = self.agent_weights.get(agent_type, 0.0)
            decision_value = analysis.decision.value

            total_weighted_score += decision_value * weight * analysis.confidence
            total_weight += weight * analysis.confidence
            total_confidence += analysis.confidence
            all_risk_factors.extend(analysis.risk_factors)

        # Normalize consensus score
        if total_weight > 0:
            consensus_score = total_weighted_score / total_weight
        else:
            consensus_score = 0.0

        # Convert consensus score to decision
        final_decision = self._score_to_decision(consensus_score)

        # Calculate consensus confidence
        avg_confidence = total_confidence / len(agent_analyses) if agent_analyses else 0.0

        # Calculate disagreement penalty
        disagreement_score = self._calculate_disagreement(agent_analyses)
        consensus_confidence = max(0.1, avg_confidence - disagreement_score * 0.2)

        # Generate risk assessment
        risk_assessment = self._assess_overall_risk(all_risk_factors, disagreement_score)

        # Generate execution recommendation
        execution_recommendation = self._generate_execution_recommendation(
            final_decision, consensus_confidence, agent_analyses
        )

        return AgentConsensus(
            final_decision=final_decision,
            consensus_confidence=consensus_confidence,
            agent_votes=agent_analyses,
            disagreement_score=disagreement_score,
            risk_assessment=risk_assessment,
            execution_recommendation=execution_recommendation
        )

    def _calculate_disagreement(self, agent_analyses: Dict[AgentType, AgentAnalysis]) -> float:
        """Calculate disagreement score between agents"""

        if len(agent_analyses) < 2:
            return 0.0

        decision_values = [analysis.decision.value for analysis in agent_analyses.values()]

        # Calculate standard deviation of decisions
        mean_decision = sum(decision_values) / len(decision_values)
        variance = sum((x - mean_decision) ** 2 for x in decision_values) / len(decision_values)
        disagreement = variance ** 0.5

        # Normalize to 0-1 scale (max disagreement is 2.0 for STRONG_BUY vs STRONG_SELL)
        return min(disagreement / 2.0, 1.0)

    def _score_to_decision(self, score: float) -> AgentDecision:
        """Convert consensus score to agent decision"""
        if score >= 0.6:
            return AgentDecision.STRONG_BUY
        elif score >= 0.2:
            return AgentDecision.BUY
        elif score <= -0.6:
            return AgentDecision.STRONG_SELL
        elif score <= -0.2:
            return AgentDecision.SELL
        else:
            return AgentDecision.NEUTRAL

    def _assess_overall_risk(self, risk_factors: List[str], disagreement: float) -> str:
        """Assess overall risk level"""

        unique_risks = list(set(risk_factors))
        risk_count = len(unique_risks)

        if disagreement > 0.6 or risk_count > 5:
            return "HIGH"
        elif disagreement > 0.3 or risk_count > 2:
            return "MODERATE"
        else:
            return "LOW"

    def _generate_execution_recommendation(self, decision: AgentDecision,
                                         confidence: float,
                                         agent_analyses: Dict[AgentType, AgentAnalysis]) -> str:
        """Generate execution recommendation based on consensus"""

        if decision == AgentDecision.NEUTRAL or confidence < 0.4:
            return "WAIT - Insufficient consensus or confidence for execution"

        # Get execution agent's specific recommendation
        execution_analysis = agent_analyses.get(AgentType.EXECUTION)
        risk_analysis = agent_analyses.get(AgentType.RISK)

        recommendations = []

        if decision in [AgentDecision.BUY, AgentDecision.STRONG_BUY]:
            recommendations.append("EXECUTE BUY")
        elif decision in [AgentDecision.SELL, AgentDecision.STRONG_SELL]:
            recommendations.append("EXECUTE SELL")

        # Add position sizing from risk agent
        if risk_analysis and "max_position_size_pct" in risk_analysis.data_points:
            size = risk_analysis.data_points["max_position_size_pct"]
            recommendations.append(f"Position size: {size:.1f}% of account")

        # Add timing considerations from execution agent
        if execution_analysis and execution_analysis.confidence > 0.6:
            recommendations.append("Timing conditions favorable")
        elif execution_analysis and execution_analysis.confidence < 0.4:
            recommendations.append("Consider waiting for better execution conditions")

        return " | ".join(recommendations)

    def _create_fallback_consensus(self, agent_analyses: Dict[AgentType, AgentAnalysis]) -> AgentConsensus:
        """Create safe fallback consensus when analysis fails"""

        return AgentConsensus(
            final_decision=AgentDecision.NEUTRAL,
            consensus_confidence=0.1,
            agent_votes=agent_analyses,
            disagreement_score=1.0,
            risk_assessment="HIGH",
            execution_recommendation="WAIT - System error, avoid trading"
        )
