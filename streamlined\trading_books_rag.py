"""
Streamlined A.T.L.A.S Trading System - RAG Trading Education
Vector database and retrieval system for trading book knowledge
"""

import asyncio
import logging
import os
from typing import Dict, List, Optional, Any
import json
import hashlib

try:
    import chromadb
    from chromadb.config import Settings
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False
    logging.warning("ChromaDB not available. Install with: pip install chromadb")

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logging.warning("OpenAI not available. Install with: pip install openai")

from config import settings, TRADING_BOOKS
from models import BookContent, EducationQuery, EducationResponse
from book_embeddings import get_all_book_content, search_book_content


class TradingEducationRAG:
    """RAG system for trading education using vector database and LLM"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.client = None
        self.collection = None
        self.openai_client = None
        self.fallback_mode = False
        
        # Initialize components
        self._initialize_vector_db()
        self._initialize_openai()
    
    def _initialize_vector_db(self):
        """Initialize ChromaDB vector database"""
        if not CHROMADB_AVAILABLE:
            self.logger.warning("ChromaDB not available, using fallback search")
            self.fallback_mode = True
            return
        
        try:
            # Create vector database directory
            os.makedirs(settings.VECTOR_DB_PATH, exist_ok=True)
            
            # Initialize ChromaDB client
            self.client = chromadb.PersistentClient(
                path=settings.VECTOR_DB_PATH,
                settings=Settings(anonymized_telemetry=False)
            )
            
            # Get or create collection
            self.collection = self.client.get_or_create_collection(
                name="trading_books",
                metadata={"description": "Trading book content for education"}
            )
            
            # Check if we need to populate the database
            if self.collection.count() == 0:
                self._populate_vector_db()
                
            self.logger.info(f"Vector database initialized with {self.collection.count()} documents")
            
        except Exception as e:
            self.logger.error(f"Error initializing vector database: {e}")
            self.fallback_mode = True
    
    def _initialize_openai(self):
        """Initialize OpenAI client"""
        if not OPENAI_AVAILABLE:
            self.logger.warning("OpenAI not available")
            return
        
        try:
            self.openai_client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)
            self.logger.info("OpenAI client initialized")
        except Exception as e:
            self.logger.error(f"Error initializing OpenAI: {e}")
    
    def _populate_vector_db(self):
        """Populate vector database with trading book content"""
        if not self.collection:
            return
        
        try:
            book_contents = get_all_book_content()
            
            documents = []
            metadatas = []
            ids = []
            
            for i, content in enumerate(book_contents):
                # Create document text
                doc_text = f"{content.book_title} - {content.chapter} - {content.section}\n\n{content.content}"
                
                # Create metadata
                metadata = {
                    "book_title": content.book_title,
                    "chapter": content.chapter,
                    "section": content.section,
                    "concepts": ",".join(content.concepts)
                }
                
                # Create unique ID
                doc_id = hashlib.md5(doc_text.encode()).hexdigest()
                
                documents.append(doc_text)
                metadatas.append(metadata)
                ids.append(doc_id)
            
            # Add to collection
            self.collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            
            self.logger.info(f"Added {len(documents)} documents to vector database")
            
        except Exception as e:
            self.logger.error(f"Error populating vector database: {e}")
    
    async def answer_education_query(self, query: EducationQuery) -> EducationResponse:
        """Answer educational query using RAG system"""
        try:
            if self.fallback_mode:
                return await self._fallback_answer(query)
            
            # Retrieve relevant documents
            relevant_docs = self._retrieve_documents(query.question, query.book_filter)
            
            if not relevant_docs:
                return EducationResponse(
                    answer="I couldn't find relevant information for your question. Please try rephrasing or asking about specific trading concepts.",
                    sources=[],
                    book_references=[],
                    confidence=0.0,
                    related_concepts=[]
                )
            
            # Generate answer using LLM
            answer = await self._generate_answer(query.question, relevant_docs)
            
            # Extract metadata
            sources = [doc.get("source", "") for doc in relevant_docs]
            book_references = list(set([doc.get("book_title", "") for doc in relevant_docs]))
            concepts = []
            for doc in relevant_docs:
                if "concepts" in doc:
                    concepts.extend(doc["concepts"].split(","))
            
            return EducationResponse(
                answer=answer,
                sources=sources[:3],  # Top 3 sources
                book_references=book_references,
                confidence=0.8,  # Simplified confidence scoring
                related_concepts=list(set(concepts))[:5]
            )
            
        except Exception as e:
            self.logger.error(f"Error answering education query: {e}")
            return EducationResponse(
                answer="I encountered an error processing your question. Please try again.",
                sources=[],
                book_references=[],
                confidence=0.0,
                related_concepts=[]
            )
    
    def _retrieve_documents(self, question: str, book_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """Retrieve relevant documents from vector database"""
        if not self.collection:
            return []
        
        try:
            # Build where clause for filtering
            where_clause = {}
            if book_filter:
                where_clause["book_title"] = {"$eq": book_filter}
            
            # Query the collection
            results = self.collection.query(
                query_texts=[question],
                n_results=5,
                where=where_clause if where_clause else None
            )
            
            # Format results
            documents = []
            if results["documents"] and len(results["documents"]) > 0:
                for i, doc in enumerate(results["documents"][0]):
                    metadata = results["metadatas"][0][i] if results["metadatas"] else {}
                    distance = results["distances"][0][i] if results["distances"] else 0
                    
                    documents.append({
                        "content": doc,
                        "source": f"{metadata.get('book_title', '')} - {metadata.get('section', '')}",
                        "book_title": metadata.get("book_title", ""),
                        "chapter": metadata.get("chapter", ""),
                        "section": metadata.get("section", ""),
                        "concepts": metadata.get("concepts", ""),
                        "relevance_score": 1 - distance  # Convert distance to relevance
                    })
            
            return documents
            
        except Exception as e:
            self.logger.error(f"Error retrieving documents: {e}")
            return []
    
    async def _generate_answer(self, question: str, relevant_docs: List[Dict[str, Any]]) -> str:
        """Generate answer using LLM with retrieved context"""
        if not self.openai_client:
            return self._simple_answer_from_docs(question, relevant_docs)
        
        try:
            # Build context from relevant documents
            context = "\n\n".join([
                f"From {doc['source']}:\n{doc['content']}"
                for doc in relevant_docs[:3]  # Use top 3 most relevant
            ])
            
            # Create prompt
            prompt = f"""You are A.T.L.A.S, an expert trading educator. Answer the following question using the provided context from trading books. 

Question: {question}

Context from trading books:
{context}

Instructions:
1. Provide a clear, educational answer that a beginner can understand
2. Include practical examples when possible
3. Reference the specific books/sources when relevant
4. If the context doesn't fully answer the question, say so and provide what information you can
5. Keep the answer focused and actionable

Answer:"""

            # Generate response
            response = self.openai_client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": "You are A.T.L.A.S, an expert trading educator helping traders learn from the wisdom of trading books."},
                    {"role": "user", "content": prompt}
                ],
                temperature=settings.OPENAI_TEMPERATURE,
                max_tokens=1000
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            self.logger.error(f"Error generating LLM answer: {e}")
            return self._simple_answer_from_docs(question, relevant_docs)
    
    def _simple_answer_from_docs(self, question: str, relevant_docs: List[Dict[str, Any]]) -> str:
        """Generate simple answer from documents without LLM"""
        if not relevant_docs:
            return "I couldn't find relevant information for your question."
        
        # Extract key information from most relevant document
        top_doc = relevant_docs[0]
        content = top_doc["content"]
        source = top_doc["source"]
        
        # Simple extraction of relevant sentences
        sentences = content.split(". ")
        question_lower = question.lower()
        
        relevant_sentences = []
        for sentence in sentences:
            if any(word in sentence.lower() for word in question_lower.split() if len(word) > 3):
                relevant_sentences.append(sentence)
        
        if relevant_sentences:
            answer = ". ".join(relevant_sentences[:3]) + "."
            return f"According to {source}:\n\n{answer}"
        else:
            return f"From {source}:\n\n{content[:500]}..."
    
    async def _fallback_answer(self, query: EducationQuery) -> EducationResponse:
        """Fallback answer using simple keyword search"""
        try:
            # Use simple keyword search
            relevant_content = search_book_content(query.question, query.book_filter)
            
            if not relevant_content:
                return EducationResponse(
                    answer="I couldn't find relevant information for your question in the trading books.",
                    sources=[],
                    book_references=[],
                    confidence=0.0,
                    related_concepts=[]
                )
            
            # Use the most relevant content
            top_content = relevant_content[0]
            
            answer = f"From {top_content.book_title}:\n\n{top_content.content[:800]}..."
            
            return EducationResponse(
                answer=answer,
                sources=[f"{top_content.book_title} - {top_content.section}"],
                book_references=[top_content.book_title],
                confidence=0.6,
                related_concepts=top_content.concepts[:5]
            )
            
        except Exception as e:
            self.logger.error(f"Error in fallback answer: {e}")
            return EducationResponse(
                answer="I encountered an error processing your question.",
                sources=[],
                book_references=[],
                confidence=0.0,
                related_concepts=[]
            )
    
    async def explain_trading_concept(self, concept: str) -> str:
        """Explain a specific trading concept"""
        query = EducationQuery(
            question=f"Explain {concept} in trading",
            context=f"I want to understand what {concept} means and how to use it in trading"
        )
        
        response = await self.answer_education_query(query)
        return response.answer
    
    async def get_book_wisdom(self, question: str, book_title: str) -> str:
        """Get wisdom from a specific trading book"""
        # Map common book names to our keys
        book_mapping = {
            "trading in the zone": "Trading in the Zone",
            "market wizards": "Market Wizards",
            "technical analysis explained": "Technical Analysis Explained",
            "how to make money in stocks": "How to Make Money in Stocks",
            "new trading for living": "The New Trading for a Living"
        }
        
        book_filter = book_mapping.get(book_title.lower(), book_title)
        
        query = EducationQuery(
            question=question,
            book_filter=book_filter
        )
        
        response = await self.answer_education_query(query)
        return response.answer
    
    def get_available_books(self) -> List[str]:
        """Get list of available trading books"""
        enhanced_books = [
            "Trading in the Zone - Mark Douglas",
            "Market Wizards - Jack Schwager",
            "Technical Analysis Explained - Martin Pring",
            "How to Make Money in Stocks - William O'Neil",
            "The New Trading for a Living - Alexander Elder",
            "Options Trading for Beginners - Comprehensive Guide",
            "Mastering the Trade - John Carter (TTM Squeeze)",
            "Advanced Technical Analysis - Professional Patterns"
        ]
        return enhanced_books
    
    def get_book_concepts(self, book_title: str) -> List[str]:
        """Get key concepts from a specific book"""
        for book_key, book_info in TRADING_BOOKS.items():
            if book_title.lower() in book_info["title"].lower():
                return book_info["key_concepts"]
        return []
